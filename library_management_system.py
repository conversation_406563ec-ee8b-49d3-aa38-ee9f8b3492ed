# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Complete Library Management System
#                   (Version 5.0 - Full Featured)
# ==============================================================================

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
from PIL import Image, ImageTk, ImageDraw, ImageFont
import sqlite3
import pandas as pd
import csv
import os
import shutil
from datetime import datetime, timedelta
import barcode
from barcode.writer import ImageWriter
import qrcode
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import json
import webbrowser
import tempfile

# ==============================================================================
# Configuration and Constants
# ==============================================================================

# Database configuration
DATABASE_FILE = 'library_system.db'

# Folders
REPORTS_FOLDER = 'reports'
BACKUP_FOLDER = 'backup'
BARCODES_FOLDER = 'member_barcodes'
ASSETS_FOLDER = 'assets'
COVERS_FOLDER = 'covers'
EXPORTS_FOLDER = 'exports'
IMPORTS_FOLDER = 'imports'

# School Information
SCHOOL_INFO = {
    'name': 'قوتابخانا ژین یا بنەرەت',
    'librarian': 'سگڤان خالد نبی',
    'principal': 'أركان خضر طه',
    'address': 'باڤێ - هەرێما کوردستانێ',
    'phone': '+964 ************'
}

# Logo files
LOGO_FILES = {
    'kurdistan': 'لۆگۆیا حکومەتا هەرێما کوردستانێ.png',
    'education': 'لۆگۆیا وەزارەتا پەروەردەیێ.png',
    'school': 'لۆگۆیا قوتابخانا ژین.png'
}

# Loan settings
LOAN_PERIOD_DAYS = 14
MAX_BOOKS_PER_STUDENT = 3

# ==============================================================================
# Database Management Class
# ==============================================================================

class DatabaseManager:
    def __init__(self, db_file=DATABASE_FILE):
        self.db_file = db_file
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Books table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS books (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                author TEXT NOT NULL,
                ddc_number TEXT,
                author_code TEXT,
                call_number TEXT,
                isbn TEXT,
                category TEXT,
                publisher TEXT,
                publication_year INTEGER,
                pages INTEGER,
                language TEXT DEFAULT 'کوردی',
                summary TEXT,
                status TEXT DEFAULT 'Available',
                location TEXT,
                date_added DATE DEFAULT CURRENT_DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Members table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                class_grade TEXT,
                national_id TEXT,
                phone TEXT,
                guardian_name TEXT,
                guardian_phone TEXT,
                address TEXT,
                email TEXT,
                registration_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'Active',
                photo_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Transactions table (borrowing/returning)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                book_barcode TEXT NOT NULL,
                member_id TEXT NOT NULL,
                transaction_type TEXT NOT NULL, -- 'borrow' or 'return'
                transaction_date DATE DEFAULT CURRENT_DATE,
                due_date DATE,
                return_date DATE,
                fine_amount REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (book_barcode) REFERENCES books (barcode),
                FOREIGN KEY (member_id) REFERENCES members (member_id)
            )
        ''')
        
        # Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert default settings
        default_settings = [
            ('loan_period_days', str(LOAN_PERIOD_DAYS), 'Default loan period in days'),
            ('max_books_per_student', str(MAX_BOOKS_PER_STUDENT), 'Maximum books per student'),
            ('fine_per_day', '250', 'Fine amount per day in IQD'),
            ('library_name', SCHOOL_INFO['name'], 'Library name'),
            ('librarian_name', SCHOOL_INFO['librarian'], 'Librarian name')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO settings (key, value, description) 
            VALUES (?, ?, ?)
        ''', default_settings)
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None, fetch=False):
        """Execute a database query"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                conn.close()
                return cursor.rowcount
        except Exception as e:
            conn.close()
            raise e
    
    def get_all_books(self):
        """Get all books from database"""
        query = "SELECT * FROM books ORDER BY title"
        return self.execute_query(query, fetch=True)
    
    def get_all_members(self):
        """Get all members from database"""
        query = "SELECT * FROM members ORDER BY name"
        return self.execute_query(query, fetch=True)
    
    def add_book(self, book_data):
        """Add a new book to database"""
        query = '''
            INSERT INTO books (barcode, title, author, ddc_number, author_code, 
                             call_number, isbn, category, publisher, publication_year, 
                             pages, language, summary, location)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, book_data)
    
    def add_member(self, member_data):
        """Add a new member to database"""
        query = '''
            INSERT INTO members (member_id, name, class_grade, national_id, phone, 
                               guardian_name, guardian_phone, address, email)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, member_data)
    
    def find_book_by_barcode(self, barcode):
        """Find book by barcode"""
        query = "SELECT * FROM books WHERE barcode = ?"
        result = self.execute_query(query, (barcode,), fetch=True)
        return result[0] if result else None
    
    def find_member_by_id(self, member_id):
        """Find member by ID"""
        query = "SELECT * FROM members WHERE member_id = ?"
        result = self.execute_query(query, (member_id,), fetch=True)
        return result[0] if result else None
    
    def search_books(self, search_term):
        """Search books by title, author, or other fields"""
        query = '''
            SELECT * FROM books 
            WHERE title LIKE ? OR author LIKE ? OR ddc_number LIKE ? 
               OR category LIKE ? OR summary LIKE ?
            ORDER BY title
        '''
        search_pattern = f'%{search_term}%'
        return self.execute_query(query, (search_pattern,) * 5, fetch=True)
    
    def search_members(self, search_term):
        """Search members by name, ID, or other fields"""
        query = '''
            SELECT * FROM members
            WHERE name LIKE ? OR member_id LIKE ? OR class_grade LIKE ?
               OR phone LIKE ? OR national_id LIKE ?
            ORDER BY name
        '''
        search_pattern = f'%{search_term}%'
        return self.execute_query(query, (search_pattern,) * 5, fetch=True)

    def borrow_book(self, book_barcode, member_id):
        """Borrow a book"""
        # Check if book exists and is available
        book = self.find_book_by_barcode(book_barcode)
        if not book:
            raise ValueError("پەرتووک نەهاتە دیتن")

        if book[14] != 'Available':  # status column
            raise ValueError("پەرتووک بەردەست نینە")

        # Check if member exists
        member = self.find_member_by_id(member_id)
        if not member:
            raise ValueError("ئەندام نەهاتە دیتن")

        # Check member's current borrowed books
        current_books = self.get_member_borrowed_books(member_id)
        if len(current_books) >= MAX_BOOKS_PER_STUDENT:
            raise ValueError(f"ئەندام نەتوانێت زیاتر لە {MAX_BOOKS_PER_STUDENT} پەرتووک ببەت")

        # Create transaction
        due_date = datetime.now() + timedelta(days=LOAN_PERIOD_DAYS)
        transaction_data = (
            book_barcode, member_id, 'borrow',
            datetime.now().strftime('%Y-%m-%d'),
            due_date.strftime('%Y-%m-%d'), None, 0, None
        )

        query = '''
            INSERT INTO transactions (book_barcode, member_id, transaction_type,
                                    transaction_date, due_date, return_date, fine_amount, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        '''
        self.execute_query(query, transaction_data)

        # Update book status
        self.execute_query("UPDATE books SET status = 'Borrowed' WHERE barcode = ?", (book_barcode,))

        return due_date

    def return_book(self, book_barcode):
        """Return a book"""
        # Find active transaction
        query = '''
            SELECT * FROM transactions
            WHERE book_barcode = ? AND transaction_type = 'borrow' AND return_date IS NULL
        '''
        transaction = self.execute_query(query, (book_barcode,), fetch=True)

        if not transaction:
            raise ValueError("ئەم پەرتووکە وەک خواستراو تۆمار نەکراوە")

        transaction = transaction[0]
        return_date = datetime.now().strftime('%Y-%m-%d')

        # Calculate fine if overdue
        due_date = datetime.strptime(transaction[5], '%Y-%m-%d')
        current_date = datetime.now()
        fine_amount = 0

        if current_date.date() > due_date.date():
            days_overdue = (current_date.date() - due_date.date()).days
            fine_amount = days_overdue * 250  # 250 IQD per day

        # Update transaction
        self.execute_query('''
            UPDATE transactions
            SET return_date = ?, fine_amount = ?
            WHERE id = ?
        ''', (return_date, fine_amount, transaction[0]))

        # Update book status
        self.execute_query("UPDATE books SET status = 'Available' WHERE barcode = ?", (book_barcode,))

        return fine_amount

    def get_member_borrowed_books(self, member_id):
        """Get currently borrowed books by member"""
        query = '''
            SELECT b.*, t.due_date, t.transaction_date
            FROM books b
            JOIN transactions t ON b.barcode = t.book_barcode
            WHERE t.member_id = ? AND t.transaction_type = 'borrow' AND t.return_date IS NULL
        '''
        return self.execute_query(query, (member_id,), fetch=True)

    def get_overdue_books(self):
        """Get all overdue books"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        query = '''
            SELECT b.title, b.author, m.name, m.member_id, t.due_date, t.transaction_date
            FROM books b
            JOIN transactions t ON b.barcode = t.book_barcode
            JOIN members m ON t.member_id = m.member_id
            WHERE t.transaction_type = 'borrow' AND t.return_date IS NULL AND t.due_date < ?
            ORDER BY t.due_date
        '''
        return self.execute_query(query, (current_date,), fetch=True)

    def get_statistics(self):
        """Get library statistics"""
        stats = {}

        # Total books
        stats['total_books'] = self.execute_query("SELECT COUNT(*) FROM books", fetch=True)[0][0]

        # Available books
        stats['available_books'] = self.execute_query(
            "SELECT COUNT(*) FROM books WHERE status = 'Available'", fetch=True)[0][0]

        # Borrowed books
        stats['borrowed_books'] = self.execute_query(
            "SELECT COUNT(*) FROM books WHERE status = 'Borrowed'", fetch=True)[0][0]

        # Total members
        stats['total_members'] = self.execute_query("SELECT COUNT(*) FROM members", fetch=True)[0][0]

        # Active members (with borrowed books)
        stats['active_members'] = self.execute_query('''
            SELECT COUNT(DISTINCT member_id) FROM transactions
            WHERE transaction_type = 'borrow' AND return_date IS NULL
        ''', fetch=True)[0][0]

        # Overdue books
        current_date = datetime.now().strftime('%Y-%m-%d')
        stats['overdue_books'] = self.execute_query('''
            SELECT COUNT(*) FROM transactions
            WHERE transaction_type = 'borrow' AND return_date IS NULL AND due_date < ?
        ''', (current_date,), fetch=True)[0][0]

        return stats

# ==============================================================================
# Utility Functions
# ==============================================================================

def ensure_folders():
    """Create necessary folders if they don't exist"""
    folders = [REPORTS_FOLDER, BACKUP_FOLDER, BARCODES_FOLDER,
               ASSETS_FOLDER, COVERS_FOLDER, EXPORTS_FOLDER, IMPORTS_FOLDER]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

def generate_barcode_image(data, filename):
    """Generate barcode image"""
    try:
        CODE39 = barcode.get_barcode_class('code39')
        code = CODE39(str(data), writer=ImageWriter(), add_checksum=False)
        code.save(filename)
        return f"{filename}.png"
    except Exception as e:
        print(f"Error generating barcode: {e}")
        return None

def generate_qr_code(data, filename):
    """Generate QR code image"""
    try:
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(data)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        img.save(f"{filename}.png")
        return f"{filename}.png"
    except Exception as e:
        print(f"Error generating QR code: {e}")
        return None

# ==============================================================================
# Data Import/Export Manager
# ==============================================================================

class DataManager:
    def __init__(self, db_manager):
        self.db = db_manager

    def import_books_from_csv(self, csv_file_path):
        """Import books from CSV file"""
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8')

            # Expected columns for books
            required_columns = ['barcode', 'title', 'author']
            optional_columns = ['ddc_number', 'author_code', 'call_number', 'isbn',
                              'category', 'publisher', 'publication_year', 'pages',
                              'language', 'summary', 'location']

            # Check required columns
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

            imported_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    # Prepare book data
                    book_data = [
                        row['barcode'],
                        row['title'],
                        row['author'],
                        row.get('ddc_number', ''),
                        row.get('author_code', ''),
                        row.get('call_number', ''),
                        row.get('isbn', ''),
                        row.get('category', ''),
                        row.get('publisher', ''),
                        row.get('publication_year', None),
                        row.get('pages', None),
                        row.get('language', 'کوردی'),
                        row.get('summary', ''),
                        row.get('location', '')
                    ]

                    self.db.add_book(book_data)
                    imported_count += 1

                except Exception as e:
                    errors.append(f"Row {index + 1}: {str(e)}")

            return imported_count, errors

        except Exception as e:
            raise Exception(f"Error importing CSV: {str(e)}")

    def import_members_from_csv(self, csv_file_path):
        """Import members from CSV file"""
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8')

            # Expected columns for members
            required_columns = ['member_id', 'name']
            optional_columns = ['class_grade', 'national_id', 'phone', 'guardian_name',
                              'guardian_phone', 'address', 'email']

            # Check required columns
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

            imported_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    # Prepare member data
                    member_data = [
                        row['member_id'],
                        row['name'],
                        row.get('class_grade', ''),
                        row.get('national_id', ''),
                        row.get('phone', ''),
                        row.get('guardian_name', ''),
                        row.get('guardian_phone', ''),
                        row.get('address', ''),
                        row.get('email', '')
                    ]

                    self.db.add_member(member_data)
                    imported_count += 1

                except Exception as e:
                    errors.append(f"Row {index + 1}: {str(e)}")

            return imported_count, errors

        except Exception as e:
            raise Exception(f"Error importing CSV: {str(e)}")

    def export_books_to_excel(self, excel_file_path):
        """Export all books to Excel file"""
        try:
            books = self.db.get_all_books()

            # Column headers - match the actual database columns
            columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                      'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                      'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added',
                      'Created At', 'Updated At']

            df = pd.DataFrame(books, columns=columns)

            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Books', index=False)

                # Format the worksheet
                worksheet = writer.sheets['Books']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return len(books)

        except Exception as e:
            raise Exception(f"Error exporting books to Excel: {str(e)}")

    def export_members_to_excel(self, excel_file_path):
        """Export all members to Excel file"""
        try:
            members = self.db.get_all_members()

            # Column headers - match the actual database columns
            columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                      'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                      'Registration Date', 'Status', 'Photo Path', 'Created At', 'Updated At']

            df = pd.DataFrame(members, columns=columns)

            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Members', index=False)

                # Format the worksheet
                worksheet = writer.sheets['Members']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            return len(members)

        except Exception as e:
            raise Exception(f"Error exporting members to Excel: {str(e)}")

    def export_complete_report_to_excel(self, excel_file_path):
        """Export complete library report to Excel with multiple sheets"""
        try:
            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                # Books sheet
                books = self.db.get_all_books()
                books_columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                               'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                               'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added']
                books_df = pd.DataFrame(books, columns=books_columns)
                books_df.to_excel(writer, sheet_name='پەرتووک', index=False)

                # Members sheet
                members = self.db.get_all_members()
                members_columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                                 'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                                 'Registration Date', 'Status']
                members_df = pd.DataFrame(members, columns=members_columns)
                members_df.to_excel(writer, sheet_name='خوێنەر', index=False)

                # Transactions sheet
                transactions_query = '''
                    SELECT t.id, t.book_barcode, b.title, t.member_id, m.name,
                           t.transaction_type, t.transaction_date, t.due_date,
                           t.return_date, t.fine_amount, t.notes
                    FROM transactions t
                    LEFT JOIN books b ON t.book_barcode = b.barcode
                    LEFT JOIN members m ON t.member_id = m.member_id
                    ORDER BY t.transaction_date DESC
                '''
                transactions = self.db.execute_query(transactions_query, fetch=True)
                transactions_columns = ['ID', 'Book Barcode', 'Book Title', 'Member ID', 'Member Name',
                                      'Type', 'Transaction Date', 'Due Date', 'Return Date',
                                      'Fine Amount', 'Notes']
                transactions_df = pd.DataFrame(transactions, columns=transactions_columns)
                transactions_df.to_excel(writer, sheet_name='بەردراو', index=False)

                # Statistics sheet
                stats = self.db.get_statistics()
                stats_data = [
                    ['کۆی گشتی پەرتووک', stats['total_books']],
                    ['پەرتووکی بەردەست', stats['available_books']],
                    ['پەرتووکی خواستراو', stats['borrowed_books']],
                    ['کۆی گشتی ئەندام', stats['total_members']],
                    ['ئەندامی چالاک', stats['active_members']],
                    ['پەرتووکی پاشکەفتی', stats['overdue_books']]
                ]
                stats_df = pd.DataFrame(stats_data, columns=['بابەت', 'ژمارە'])
                stats_df.to_excel(writer, sheet_name='ئامار', index=False)

            return True

        except Exception as e:
            raise Exception(f"Error exporting complete report: {str(e)}")

# ==============================================================================
# ID Card and Label Generator
# ==============================================================================

class IDCardGenerator:
    def __init__(self, db_manager):
        self.db = db_manager
        self.card_width = 600
        self.card_height = 380
        self.label_width = 400
        self.label_height = 300

    def load_logo(self, logo_path, size=(60, 60)):
        """Load and resize logo image"""
        try:
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize(size, Image.Resampling.LANCZOS)
                return img
            else:
                # Create placeholder if logo not found
                img = Image.new('RGBA', size, (200, 200, 200, 255))
                draw = ImageDraw.Draw(img)
                draw.text((size[0]//2, size[1]//2), "LOGO", fill=(100, 100, 100), anchor="mm")
                return img
        except Exception as e:
            print(f"Error loading logo {logo_path}: {e}")
            # Return placeholder
            img = Image.new('RGBA', size, (200, 200, 200, 255))
            return img

    def generate_member_id_card(self, member_id, output_path=None):
        """Generate membership ID card for a member"""
        try:
            member = self.db.find_member_by_id(member_id)
            if not member:
                raise ValueError("ئەندام نەهاتە دیتن")

            # Create card image
            card = Image.new('RGB', (self.card_width, self.card_height), 'white')
            draw = ImageDraw.Draw(card)

            # Load logos
            kurdistan_logo = self.load_logo(LOGO_FILES['kurdistan'], (50, 50))
            education_logo = self.load_logo(LOGO_FILES['education'], (50, 50))
            school_logo = self.load_logo(LOGO_FILES['school'], (50, 50))

            # Paste logos at top
            card.paste(school_logo, (50, 20), school_logo if school_logo.mode == 'RGBA' else None)
            card.paste(education_logo, (275, 20), education_logo if education_logo.mode == 'RGBA' else None)
            card.paste(kurdistan_logo, (500, 20), kurdistan_logo if kurdistan_logo.mode == 'RGBA' else None)

            # School name
            try:
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_medium = ImageFont.truetype("arial.ttf", 12)
                font_small = ImageFont.truetype("arial.ttf", 10)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()

            # Header
            draw.text((300, 85), SCHOOL_INFO['name'], fill='black', font=font_large, anchor="mm")
            draw.line([(50, 105), (550, 105)], fill='black', width=2)

            # Card title
            draw.text((300, 125), "کارتا ئەندامەتیێ - پەرتووکخانا ژین", fill='blue', font=font_medium, anchor="mm")

            # Photo placeholder
            photo_box = (80, 150, 180, 250)
            draw.rectangle(photo_box, outline='black', width=2)
            draw.text((130, 200), "وێنە", fill='gray', font=font_medium, anchor="mm")

            # Member information
            info_x = 200
            info_y = 160
            line_height = 20

            member_info = [
                f"ناڤ: {member[2]}",  # name
                f"پۆل: {member[3] or 'نەدیارکراو'}",  # class_grade
                f"ژمارا ئەندامەتیێ: {member[1]}",  # member_id
            ]

            for i, info in enumerate(member_info):
                draw.text((info_x, info_y + i * line_height), info, fill='black', font=font_medium)

            # Generate barcode
            barcode_path = os.path.join(BARCODES_FOLDER, f"member_{member_id}")
            barcode_file = generate_barcode_image(member_id, barcode_path)

            if barcode_file and os.path.exists(barcode_file):
                barcode_img = Image.open(barcode_file)
                barcode_img = barcode_img.resize((200, 60), Image.Resampling.LANCZOS)
                card.paste(barcode_img, (200, 260))

            # Member ID below barcode
            draw.text((300, 330), member_id, fill='black', font=font_medium, anchor="mm")

            # Footer
            draw.line([(50, 350), (550, 350)], fill='black', width=1)
            footer_text = [
                "- ماوێ خواستنێ: ٢ حەفتی.",
                "- پاراستنا پاقژیا پەرتووکێ ئەرکێ تەیە."
            ]

            for i, text in enumerate(footer_text):
                draw.text((60, 355 + i * 12), text, fill='black', font=font_small)

            # Save card
            if not output_path:
                output_path = os.path.join(EXPORTS_FOLDER, f"member_card_{member_id}.png")

            card.save(output_path)
            return output_path

        except Exception as e:
            raise Exception(f"Error generating member ID card: {str(e)}")

    def generate_book_label(self, book_barcode, output_path=None):
        """Generate book spine label"""
        try:
            book = self.db.find_book_by_barcode(book_barcode)
            if not book:
                raise ValueError("پەرتووک نەهاتە دیتن")

            # Create label image
            label = Image.new('RGB', (self.label_width, self.label_height), 'white')
            draw = ImageDraw.Draw(label)

            # Border
            draw.rectangle([(5, 5), (self.label_width-5, self.label_height-5)], outline='black', width=2)

            try:
                font_large = ImageFont.truetype("arial.ttf", 14)
                font_medium = ImageFont.truetype("arial.ttf", 12)
                font_small = ImageFont.truetype("arial.ttf", 8)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()

            # DDC Number (top)
            ddc_number = book[4] or "000"  # ddc_number column
            draw.text((200, 30), ddc_number, fill='black', font=font_large, anchor="mm")

            # Author Code
            author_code = book[5] or "XXX"  # author_code column
            draw.text((200, 55), author_code, fill='black', font=font_medium, anchor="mm")

            # Separator line
            draw.line([(20, 80), (380, 80)], fill='black', width=1)

            # Generate barcode
            barcode_path = os.path.join(BARCODES_FOLDER, f"book_{book_barcode}")
            barcode_file = generate_barcode_image(book_barcode, barcode_path)

            if barcode_file and os.path.exists(barcode_file):
                barcode_img = Image.open(barcode_file)
                barcode_img = barcode_img.resize((150, 40), Image.Resampling.LANCZOS)
                label.paste(barcode_img, (125, 100))

            # Barcode number
            draw.text((200, 150), book_barcode, fill='black', font=font_medium, anchor="mm")

            # Separator line
            draw.line([(20, 180), (380, 180)], fill='black', width=1)

            # Library name
            draw.text((200, 210), "پەرتووکخانا ژین", fill='black', font=font_small, anchor="mm")

            # Save label
            if not output_path:
                output_path = os.path.join(EXPORTS_FOLDER, f"book_label_{book_barcode}.png")

            label.save(output_path)
            return output_path

        except Exception as e:
            raise Exception(f"Error generating book label: {str(e)}")

    def generate_all_member_cards(self):
        """Generate ID cards for all members"""
        try:
            members = self.db.get_all_members()
            generated_cards = []

            for member in members:
                member_id = member[1]  # member_id column
                try:
                    card_path = self.generate_member_id_card(member_id)
                    generated_cards.append(card_path)
                except Exception as e:
                    print(f"Error generating card for member {member_id}: {e}")

            return generated_cards

        except Exception as e:
            raise Exception(f"Error generating all member cards: {str(e)}")

    def create_cards_html_page(self, card_paths):
        """Create HTML page with all member cards for printing"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>کارتەکانی ئەندامەتی - {SCHOOL_INFO['name']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .card-container {{ display: flex; flex-wrap: wrap; gap: 20px; }}
                    .card {{ border: 1px solid #ccc; padding: 10px; }}
                    .card img {{ max-width: 100%; height: auto; }}
                    @media print {{
                        .card {{ page-break-inside: avoid; }}
                    }}
                </style>
            </head>
            <body>
                <h1>کارتەکانی ئەندامەتی - {SCHOOL_INFO['name']}</h1>
                <p>بەرواری دروستکرن: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
                <div class="card-container">
            """

            for card_path in card_paths:
                if os.path.exists(card_path):
                    card_name = os.path.basename(card_path)
                    html_content += f"""
                    <div class="card">
                        <img src="{card_path}" alt="{card_name}">
                        <p>{card_name}</p>
                    </div>
                    """

            html_content += """
                </div>
            </body>
            </html>
            """

            html_file_path = os.path.join(EXPORTS_FOLDER, "member_cards.html")
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return html_file_path

        except Exception as e:
            raise Exception(f"Error creating HTML page: {str(e)}")

# ==============================================================================
# Main GUI Application
# ==============================================================================

class LibraryManagementApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ")
        self.root.geometry("1400x800")
        self.root.state('zoomed')  # Maximize window on Windows

        # Initialize managers
        ensure_folders()
        self.db = DatabaseManager()
        self.data_manager = DataManager(self.db)
        self.id_generator = IDCardGenerator(self.db)

        # Configure styles
        self.setup_styles()

        # Create GUI
        self.create_widgets()

        # Initialize data
        self.refresh_data()

    def setup_styles(self):
        """Setup custom styles for the application"""
        self.style = ttk.Style()

        # Configure colors
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'danger': '#C73E1D',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }

        # Configure button styles
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           font=('Arial', 10, 'bold'))

        self.style.configure('Success.TButton',
                           background=self.colors['success'],
                           foreground='white',
                           font=('Arial', 10, 'bold'))

        self.style.configure('Danger.TButton',
                           background=self.colors['danger'],
                           foreground='white',
                           font=('Arial', 10, 'bold'))

    def create_widgets(self):
        """Create main GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Left panel for quick actions
        left_panel = ttk.LabelFrame(main_frame, text="عملیاتی خێرا", padding=10)
        left_panel.pack(side='left', fill='y', padx=(0, 10))

        # Right panel for main content
        right_panel = ttk.Frame(main_frame)
        right_panel.pack(side='left', fill='both', expand=True)

        self.create_left_panel(left_panel)
        self.create_right_panel(right_panel)

    def create_left_panel(self, parent):
        """Create left panel with quick actions"""
        # Circulation section
        circulation_frame = ttk.LabelFrame(parent, text="خواستن و ڤەگەڕاندن", padding=10)
        circulation_frame.pack(fill='x', pady=(0, 10))

        # Student ID entry
        ttk.Label(circulation_frame, text="ژمارا ئەندامی:").pack(anchor='w')
        self.student_id_var = tk.StringVar()
        self.student_id_entry = ttk.Entry(circulation_frame, textvariable=self.student_id_var, width=25)
        self.student_id_entry.pack(fill='x', pady=(0, 5))
        self.student_id_entry.bind('<KeyRelease>', self.on_student_id_change)

        # Book barcode entry
        ttk.Label(circulation_frame, text="بارکۆدی پەرتووک:").pack(anchor='w')
        self.book_barcode_var = tk.StringVar()
        self.book_barcode_entry = ttk.Entry(circulation_frame, textvariable=self.book_barcode_var, width=25)
        self.book_barcode_entry.pack(fill='x', pady=(0, 10))
        self.book_barcode_entry.bind('<KeyRelease>', self.on_book_barcode_change)

        # Action buttons
        button_frame = ttk.Frame(circulation_frame)
        button_frame.pack(fill='x')

        ttk.Button(button_frame, text="پەرتووک بدە",
                  style='Success.TButton',
                  command=self.borrow_book).pack(side='left', padx=(0, 5))

        ttk.Button(button_frame, text="پەرتووک وەرگرە",
                  style='Primary.TButton',
                  command=self.return_book).pack(side='left')

        # Info display
        info_frame = ttk.LabelFrame(parent, text="زانیاری", padding=10)
        info_frame.pack(fill='both', expand=True, pady=(0, 10))

        self.student_info_label = ttk.Label(info_frame, text="ئەندام: هیچ کەسێک دیارنەکراوە",
                                          font=('Arial', 9, 'bold'))
        self.student_info_label.pack(anchor='w', pady=(0, 5))

        self.book_info_label = ttk.Label(info_frame, text="پەرتووک: هیچ پەرتووکێک دیارنەکراوە",
                                       font=('Arial', 9, 'bold'))
        self.book_info_label.pack(anchor='w', pady=(0, 10))

        # Statistics
        stats_frame = ttk.LabelFrame(info_frame, text="ئامار", padding=5)
        stats_frame.pack(fill='x')

        self.stats_text = tk.Text(stats_frame, height=8, width=30, font=('Arial', 8))
        self.stats_text.pack(fill='both', expand=True)

        # Logo section
        logo_frame = ttk.Frame(parent)
        logo_frame.pack(side='bottom', fill='x', pady=10)

        self.load_logos(logo_frame)

    def create_right_panel(self, parent):
        """Create right panel with tabbed interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill='both', expand=True)

        # Create tabs
        self.create_search_tab()
        self.create_books_tab()
        self.create_members_tab()
        self.create_reports_tab()
        self.create_settings_tab()

    def create_search_tab(self):
        """Create search and browse tab"""
        search_frame = ttk.Frame(self.notebook)
        self.notebook.add(search_frame, text="گەڕان و بینین")

        # Search bar
        search_bar_frame = ttk.Frame(search_frame)
        search_bar_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(search_bar_frame, text="گەڕان:").pack(side='left')
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_bar_frame, textvariable=self.search_var, width=40)
        search_entry.pack(side='left', padx=(5, 0), fill='x', expand=True)
        search_entry.bind('<KeyRelease>', self.on_search_change)

        # Search type selection
        search_type_frame = ttk.Frame(search_bar_frame)
        search_type_frame.pack(side='right', padx=(10, 0))

        self.search_type_var = tk.StringVar(value="books")
        ttk.Radiobutton(search_type_frame, text="پەرتووک", variable=self.search_type_var,
                       value="books", command=self.refresh_search).pack(side='left')
        ttk.Radiobutton(search_type_frame, text="ئەندام", variable=self.search_type_var,
                       value="members", command=self.refresh_search).pack(side='left', padx=(10, 0))

        # Results area
        results_frame = ttk.Frame(search_frame)
        results_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # Results list
        list_frame = ttk.Frame(results_frame)
        list_frame.pack(side='left', fill='y', padx=(0, 10))

        ttk.Label(list_frame, text="ئەنجامەکان:").pack(anchor='w')

        self.results_listbox = tk.Listbox(list_frame, width=50, height=20, font=('Arial', 9))
        results_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.results_listbox.yview)
        self.results_listbox.config(yscrollcommand=results_scrollbar.set)

        self.results_listbox.pack(side='left', fill='both', expand=True)
        results_scrollbar.pack(side='right', fill='y')

        self.results_listbox.bind('<<ListboxSelect>>', self.on_result_select)

        # Details area
        details_frame = ttk.LabelFrame(results_frame, text="وردەکاری", padding=10)
        details_frame.pack(side='left', fill='both', expand=True)

        self.details_text = tk.Text(details_frame, wrap='word', font=('Arial', 10))
        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_text.yview)
        self.details_text.config(yscrollcommand=details_scrollbar.set)

        self.details_text.pack(side='left', fill='both', expand=True)
        details_scrollbar.pack(side='right', fill='y')

    def create_books_tab(self):
        """Create books management tab"""
        books_frame = ttk.Frame(self.notebook)
        self.notebook.add(books_frame, text="بەڕێوەبردنی پەرتووک")

        # Buttons frame
        buttons_frame = ttk.Frame(books_frame)
        buttons_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(buttons_frame, text="پەرتووکی نوێ زیاد بکە",
                  style='Primary.TButton',
                  command=self.add_book_dialog).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="هاوردەکردن لە CSV",
                  command=self.import_books_csv).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="هەناردەکردن بۆ Excel",
                  command=self.export_books_excel).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="دروستکردنی لیبڵی پەرتووک",
                  style='Success.TButton',
                  command=self.generate_book_labels).pack(side='left')

        # Books list
        list_frame = ttk.Frame(books_frame)
        list_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # Treeview for books
        columns = ('Barcode', 'Title', 'Author', 'Category', 'Status')
        self.books_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)

        # Configure columns
        self.books_tree.heading('Barcode', text='بارکۆد')
        self.books_tree.heading('Title', text='ناونیشان')
        self.books_tree.heading('Author', text='نووسەر')
        self.books_tree.heading('Category', text='جۆر')
        self.books_tree.heading('Status', text='دۆخ')

        self.books_tree.column('Barcode', width=100)
        self.books_tree.column('Title', width=300)
        self.books_tree.column('Author', width=200)
        self.books_tree.column('Category', width=150)
        self.books_tree.column('Status', width=100)

        # Scrollbars
        books_v_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.books_tree.yview)
        books_h_scrollbar = ttk.Scrollbar(list_frame, orient='horizontal', command=self.books_tree.xview)
        self.books_tree.config(yscrollcommand=books_v_scrollbar.set, xscrollcommand=books_h_scrollbar.set)

        self.books_tree.pack(side='left', fill='both', expand=True)
        books_v_scrollbar.pack(side='right', fill='y')
        books_h_scrollbar.pack(side='bottom', fill='x')

    def create_members_tab(self):
        """Create members management tab"""
        members_frame = ttk.Frame(self.notebook)
        self.notebook.add(members_frame, text="بەڕێوەبردنی ئەندام")

        # Buttons frame
        buttons_frame = ttk.Frame(members_frame)
        buttons_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(buttons_frame, text="ئەندامی نوێ زیاد بکە",
                  style='Primary.TButton',
                  command=self.add_member_dialog).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="هاوردەکردن لە CSV",
                  command=self.import_members_csv).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="هەناردەکردن بۆ Excel",
                  command=self.export_members_excel).pack(side='left', padx=(0, 10))

        ttk.Button(buttons_frame, text="دروستکردنی کارتی ئەندامەتی",
                  style='Success.TButton',
                  command=self.generate_member_cards).pack(side='left')

        # Members list
        list_frame = ttk.Frame(members_frame)
        list_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # Treeview for members
        columns = ('MemberID', 'Name', 'Class', 'Phone', 'Status')
        self.members_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=20)

        # Configure columns
        self.members_tree.heading('MemberID', text='ژمارەی ئەندام')
        self.members_tree.heading('Name', text='ناو')
        self.members_tree.heading('Class', text='پۆل')
        self.members_tree.heading('Phone', text='تەلەفۆن')
        self.members_tree.heading('Status', text='دۆخ')

        self.members_tree.column('MemberID', width=120)
        self.members_tree.column('Name', width=200)
        self.members_tree.column('Class', width=100)
        self.members_tree.column('Phone', width=150)
        self.members_tree.column('Status', width=100)

        # Scrollbars
        members_v_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.members_tree.yview)
        members_h_scrollbar = ttk.Scrollbar(list_frame, orient='horizontal', command=self.members_tree.xview)
        self.members_tree.config(yscrollcommand=members_v_scrollbar.set, xscrollcommand=members_h_scrollbar.set)

        self.members_tree.pack(side='left', fill='both', expand=True)
        members_v_scrollbar.pack(side='right', fill='y')
        members_h_scrollbar.pack(side='bottom', fill='x')

    def create_reports_tab(self):
        """Create reports tab"""
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="ڕاپۆرت")

        # Reports buttons
        reports_buttons_frame = ttk.Frame(reports_frame)
        reports_buttons_frame.pack(fill='x', padx=10, pady=10)

        # Row 1
        row1_frame = ttk.Frame(reports_buttons_frame)
        row1_frame.pack(fill='x', pady=(0, 10))

        ttk.Button(row1_frame, text="ڕاپۆرتی پەرتووکی پاشکەوت",
                  command=self.generate_overdue_report).pack(side='left', padx=(0, 10))

        ttk.Button(row1_frame, text="ڕاپۆرتی ئەندامی چالاک",
                  command=self.generate_active_members_report).pack(side='left', padx=(0, 10))

        ttk.Button(row1_frame, text="ڕاپۆرتی ئاماری گشتی",
                  command=self.generate_statistics_report).pack(side='left')

        # Row 2
        row2_frame = ttk.Frame(reports_buttons_frame)
        row2_frame.pack(fill='x')

        ttk.Button(row2_frame, text="هەناردەکردنی تەواوی داتا بۆ Excel",
                  style='Success.TButton',
                  command=self.export_complete_data).pack(side='left', padx=(0, 10))

        ttk.Button(row2_frame, text="یەدەگ گرتن",
                  style='Danger.TButton',
                  command=self.backup_data).pack(side='left')

        # Report display area
        report_frame = ttk.LabelFrame(reports_frame, text="ڕاپۆرت", padding=10)
        report_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        self.report_text = tk.Text(report_frame, wrap='word', font=('Arial', 10))
        report_scrollbar = ttk.Scrollbar(report_frame, orient='vertical', command=self.report_text.yview)
        self.report_text.config(yscrollcommand=report_scrollbar.set)

        self.report_text.pack(side='left', fill='both', expand=True)
        report_scrollbar.pack(side='right', fill='y')

    def create_settings_tab(self):
        """Create settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="ڕێکخستن")

        # Settings content
        settings_content = ttk.LabelFrame(settings_frame, text="ڕێکخستنەکانی سیستەم", padding=20)
        settings_content.pack(fill='both', expand=True, padx=10, pady=10)

        # School information
        school_frame = ttk.LabelFrame(settings_content, text="زانیاری قوتابخانە", padding=10)
        school_frame.pack(fill='x', pady=(0, 20))

        ttk.Label(school_frame, text=f"ناوی قوتابخانە: {SCHOOL_INFO['name']}").pack(anchor='w', pady=2)
        ttk.Label(school_frame, text=f"کتێبخانەدار: {SCHOOL_INFO['librarian']}").pack(anchor='w', pady=2)
        ttk.Label(school_frame, text=f"بەڕێوەبەر: {SCHOOL_INFO['principal']}").pack(anchor='w', pady=2)
        ttk.Label(school_frame, text=f"ناونیشان: {SCHOOL_INFO['address']}").pack(anchor='w', pady=2)

        # System settings
        system_frame = ttk.LabelFrame(settings_content, text="ڕێکخستنەکانی سیستەم", padding=10)
        system_frame.pack(fill='x', pady=(0, 20))

        ttk.Label(system_frame, text=f"ماوەی خواستن: {LOAN_PERIOD_DAYS} ڕۆژ").pack(anchor='w', pady=2)
        ttk.Label(system_frame, text=f"زۆرترین پەرتووک بۆ هەر ئەندامێک: {MAX_BOOKS_PER_STUDENT}").pack(anchor='w', pady=2)
        ttk.Label(system_frame, text="نرخی جەریمە: ٢٥٠ دینار بۆ هەر ڕۆژێک").pack(anchor='w', pady=2)

        # Database info
        db_frame = ttk.LabelFrame(settings_content, text="زانیاری بنکەی داتا", padding=10)
        db_frame.pack(fill='x')

        stats = self.db.get_statistics()
        ttk.Label(db_frame, text=f"کۆی گشتی پەرتووک: {stats['total_books']}").pack(anchor='w', pady=2)
        ttk.Label(db_frame, text=f"کۆی گشتی ئەندام: {stats['total_members']}").pack(anchor='w', pady=2)
        ttk.Label(db_frame, text=f"پەرتووکی خواستراو: {stats['borrowed_books']}").pack(anchor='w', pady=2)
        ttk.Label(db_frame, text=f"پەرتووکی پاشکەوت: {stats['overdue_books']}").pack(anchor='w', pady=2)

    def load_logos(self, parent):
        """Load and display logos"""
        try:
            logo_frame = ttk.Frame(parent)
            logo_frame.pack(fill='x')

            # Try to load logos
            logos = []
            for logo_name, logo_file in LOGO_FILES.items():
                try:
                    if os.path.exists(logo_file):
                        img = Image.open(logo_file)
                        img = img.resize((40, 40), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(img)
                        logos.append(photo)

                        label = ttk.Label(logo_frame, image=photo)
                        label.image = photo  # Keep a reference
                        label.pack(side='left', padx=5)
                except Exception as e:
                    print(f"Error loading logo {logo_file}: {e}")

            if not logos:
                ttk.Label(logo_frame, text="لۆگۆکان بەردەست نین").pack()

        except Exception as e:
            print(f"Error in load_logos: {e}")

    # Event Handlers
    def on_student_id_change(self, event=None):
        """Handle student ID entry change"""
        student_id = self.student_id_var.get().strip()
        if student_id:
            member = self.db.find_member_by_id(student_id)
            if member:
                self.student_info_label.config(
                    text=f"ئەندام: {member[2]} - {member[3] or 'نەدیارکراو'}")
            else:
                self.student_info_label.config(text="ئەندام: نەدۆزرایەوە!")
        else:
            self.student_info_label.config(text="ئەندام: هیچ کەسێک دیارنەکراوە")

    def on_book_barcode_change(self, event=None):
        """Handle book barcode entry change"""
        barcode = self.book_barcode_var.get().strip()
        if barcode:
            book = self.db.find_book_by_barcode(barcode)
            if book:
                self.book_info_label.config(text=f"پەرتووک: {book[2]} - {book[14]}")
            else:
                self.book_info_label.config(text="پەرتووک: نەدۆزرایەوە!")
        else:
            self.book_info_label.config(text="پەرتووک: هیچ پەرتووکێک دیارنەکراوە")

    def on_search_change(self, event=None):
        """Handle search entry change"""
        self.refresh_search()

    def on_result_select(self, event=None):
        """Handle result selection"""
        selection = self.results_listbox.curselection()
        if selection:
            index = selection[0]
            if hasattr(self, 'search_results') and index < len(self.search_results):
                item = self.search_results[index]
                self.display_item_details(item)

    def refresh_search(self):
        """Refresh search results"""
        search_term = self.search_var.get().strip()
        search_type = self.search_type_var.get()

        self.results_listbox.delete(0, tk.END)
        self.details_text.delete('1.0', tk.END)

        if not search_term:
            return

        try:
            if search_type == "books":
                results = self.db.search_books(search_term)
                self.search_results = results
                for book in results:
                    display_text = f"{book[2]} - {book[3]} ({book[14]})"
                    self.results_listbox.insert(tk.END, display_text)
            else:  # members
                results = self.db.search_members(search_term)
                self.search_results = results
                for member in results:
                    display_text = f"{member[2]} - {member[3] or 'نەدیارکراو'} ({member[1]})"
                    self.results_listbox.insert(tk.END, display_text)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە گەڕان: {str(e)}")

    def display_item_details(self, item):
        """Display details of selected item"""
        self.details_text.delete('1.0', tk.END)

        if self.search_type_var.get() == "books":
            # Display book details
            details = f"""ناونیشان: {item[2]}
نووسەر: {item[3]}
بارکۆد: {item[1]}
ژمارەی DDC: {item[4] or 'نەدیارکراو'}
کۆدی نووسەر: {item[5] or 'نەدیارکراو'}
ISBN: {item[7] or 'نەدیارکراو'}
جۆر: {item[8] or 'نەدیارکراو'}
دەرچوون: {item[9] or 'نەدیارکراو'}
ساڵی دەرچوون: {item[10] or 'نەدیارکراو'}
لاپەڕە: {item[11] or 'نەدیارکراو'}
زمان: {item[12] or 'کوردی'}
دۆخ: {item[14]}
شوێن: {item[15] or 'نەدیارکراو'}
بەرواری زیادکردن: {item[16]}

پوختە:
{item[13] or 'هیچ پوختەیەک نەدراوە'}"""
        else:
            # Display member details
            details = f"""ناو: {item[2]}
ژمارەی ئەندام: {item[1]}
پۆل: {item[3] or 'نەدیارکراو'}
ژمارەی ناسنامە: {item[4] or 'نەدیارکراو'}
تەلەفۆن: {item[5] or 'نەدیارکراو'}
ناوی سەرپەرشتیار: {item[6] or 'نەدیارکراو'}
تەلەفۆنی سەرپەرشتیار: {item[7] or 'نەدیارکراو'}
ناونیشان: {item[8] or 'نەدیارکراو'}
ئیمەیڵ: {item[9] or 'نەدیارکراو'}
بەرواری تۆمارکردن: {item[10]}
دۆخ: {item[11]}"""

        self.details_text.insert('1.0', details)

    # Action Methods
    def borrow_book(self):
        """Handle book borrowing"""
        student_id = self.student_id_var.get().strip()
        book_barcode = self.book_barcode_var.get().strip()

        if not student_id or not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە ژمارەی ئەندام و بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            due_date = self.db.borrow_book(book_barcode, student_id)
            messagebox.showinfo("سەرکەوتوو",
                              f"پەرتووک بە سەرکەوتوویی خواسترا\nبەرواری گەڕاندنەوە: {due_date.strftime('%Y-%m-%d')}")

            # Clear entries
            self.student_id_var.set("")
            self.book_barcode_var.set("")
            self.student_info_label.config(text="ئەندام: هیچ کەسێک دیارنەکراوە")
            self.book_info_label.config(text="پەرتووک: هیچ پەرتووکێک دیارنەکراوە")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە خواستنی پەرتووک: {str(e)}")

    def return_book(self):
        """Handle book returning"""
        book_barcode = self.book_barcode_var.get().strip()

        if not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            fine_amount = self.db.return_book(book_barcode)

            if fine_amount > 0:
                messagebox.showinfo("سەرکەوتوو",
                                  f"پەرتووک بە سەرکەوتوویی گەڕێندرایەوە\nجەریمە: {fine_amount} دینار")
            else:
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی گەڕێندرایەوە")

            # Clear entries
            self.book_barcode_var.set("")
            self.book_info_label.config(text="پەرتووک: هیچ پەرتووکێک دیارنەکراوە")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە گەڕاندنەوەی پەرتووک: {str(e)}")

    def refresh_data(self):
        """Refresh all data displays"""
        try:
            # Refresh statistics
            stats = self.db.get_statistics()
            stats_text = f"""کۆی گشتی پەرتووک: {stats['total_books']}
پەرتووکی بەردەست: {stats['available_books']}
پەرتووکی خواستراو: {stats['borrowed_books']}
کۆی گشتی ئەندام: {stats['total_members']}
ئەندامی چالاک: {stats['active_members']}
پەرتووکی پاشکەوت: {stats['overdue_books']}"""

            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', stats_text)

            # Refresh books tree
            if hasattr(self, 'books_tree'):
                for item in self.books_tree.get_children():
                    self.books_tree.delete(item)

                books = self.db.get_all_books()
                for book in books:
                    self.books_tree.insert('', 'end', values=(
                        book[1],  # barcode
                        book[2],  # title
                        book[3],  # author
                        book[8] or 'نەدیارکراو',  # category
                        book[14]  # status
                    ))

            # Refresh members tree
            if hasattr(self, 'members_tree'):
                for item in self.members_tree.get_children():
                    self.members_tree.delete(item)

                members = self.db.get_all_members()
                for member in members:
                    self.members_tree.insert('', 'end', values=(
                        member[1],  # member_id
                        member[2],  # name
                        member[3] or 'نەدیارکراو',  # class_grade
                        member[5] or 'نەدیارکراو',  # phone
                        member[11]  # status
                    ))

        except Exception as e:
            print(f"Error refreshing data: {e}")

    # Dialog Methods
    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("زیادکردنی پەرتووکی نوێ")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create form
        form_frame = ttk.Frame(dialog, padding=20)
        form_frame.pack(fill='both', expand=True)

        # Form fields
        fields = {}

        # Required fields
        required_fields = [
            ('barcode', 'بارکۆد *'),
            ('title', 'ناونیشان *'),
            ('author', 'نووسەر *')
        ]

        # Optional fields
        optional_fields = [
            ('ddc_number', 'ژمارەی DDC'),
            ('author_code', 'کۆدی نووسەر'),
            ('isbn', 'ISBN'),
            ('category', 'جۆر'),
            ('publisher', 'دەرچوون'),
            ('publication_year', 'ساڵی دەرچوون'),
            ('pages', 'ژمارەی لاپەڕە'),
            ('language', 'زمان'),
            ('location', 'شوێن'),
            ('summary', 'پوختە')
        ]

        row = 0
        for field_name, label_text in required_fields + optional_fields:
            ttk.Label(form_frame, text=label_text).grid(row=row, column=0, sticky='w', pady=5)

            if field_name == 'summary':
                entry = tk.Text(form_frame, height=4, width=40)
                entry.grid(row=row, column=1, sticky='ew', pady=5)
            else:
                entry = ttk.Entry(form_frame, width=40)
                entry.grid(row=row, column=1, sticky='ew', pady=5)

            fields[field_name] = entry
            row += 1

        # Set default language
        fields['language'].insert(0, 'کوردی')

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_book():
            try:
                # Get values
                barcode = fields['barcode'].get().strip()
                title = fields['title'].get().strip()
                author = fields['author'].get().strip()

                if not barcode or not title or not author:
                    messagebox.showwarning("ئاگاداری", "تکایە خانەکانی پێویست پڕ بکەرەوە")
                    return

                # Prepare data
                book_data = [
                    barcode, title, author,
                    fields['ddc_number'].get().strip(),
                    fields['author_code'].get().strip(),
                    f"{fields['ddc_number'].get().strip()} {fields['author_code'].get().strip()}".strip(),
                    fields['isbn'].get().strip(),
                    fields['category'].get().strip(),
                    fields['publisher'].get().strip(),
                    int(fields['publication_year'].get().strip()) if fields['publication_year'].get().strip().isdigit() else None,
                    int(fields['pages'].get().strip()) if fields['pages'].get().strip().isdigit() else None,
                    fields['language'].get().strip() or 'کوردی',
                    fields['summary'].get('1.0', tk.END).strip(),
                    fields['location'].get().strip()
                ]

                self.db.add_book(book_data)
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی پەرتووک: {str(e)}")

        ttk.Button(button_frame, text="پاشەکەوتکردن",
                  style='Primary.TButton', command=save_book).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="پاشگەزبوونەوە",
                  command=dialog.destroy).pack(side='left')

        form_frame.columnconfigure(1, weight=1)

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("زیادکردنی ئەندامی نوێ")
        dialog.geometry("500x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create form
        form_frame = ttk.Frame(dialog, padding=20)
        form_frame.pack(fill='both', expand=True)

        # Form fields
        fields = {}

        field_info = [
            ('member_id', 'ژمارەی ئەندام *'),
            ('name', 'ناو *'),
            ('class_grade', 'پۆل'),
            ('national_id', 'ژمارەی ناسنامە'),
            ('phone', 'تەلەفۆن'),
            ('guardian_name', 'ناوی سەرپەرشتیار'),
            ('guardian_phone', 'تەلەفۆنی سەرپەرشتیار'),
            ('address', 'ناونیشان'),
            ('email', 'ئیمەیڵ')
        ]

        row = 0
        for field_name, label_text in field_info:
            ttk.Label(form_frame, text=label_text).grid(row=row, column=0, sticky='w', pady=5)
            entry = ttk.Entry(form_frame, width=40)
            entry.grid(row=row, column=1, sticky='ew', pady=5)
            fields[field_name] = entry
            row += 1

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)

        def save_member():
            try:
                # Get values
                member_id = fields['member_id'].get().strip()
                name = fields['name'].get().strip()

                if not member_id or not name:
                    messagebox.showwarning("ئاگاداری", "تکایە خانەکانی پێویست پڕ بکەرەوە")
                    return

                # Prepare data
                member_data = [
                    member_id, name,
                    fields['class_grade'].get().strip(),
                    fields['national_id'].get().strip(),
                    fields['phone'].get().strip(),
                    fields['guardian_name'].get().strip(),
                    fields['guardian_phone'].get().strip(),
                    fields['address'].get().strip(),
                    fields['email'].get().strip()
                ]

                self.db.add_member(member_data)
                messagebox.showinfo("سەرکەوتوو", "ئەندام بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی ئەندام: {str(e)}")

        ttk.Button(button_frame, text="پاشەکەوتکردن",
                  style='Primary.TButton', command=save_member).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="پاشگەزبوونەوە",
                  command=dialog.destroy).pack(side='left')

        form_frame.columnconfigure(1, weight=1)

    # Import/Export Methods
    def import_books_csv(self):
        """Import books from CSV file"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ پەرتووک",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_books_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} پەرتووک هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو:\n" + "\n".join(errors[:5])
                    if len(errors) > 5:
                        message += f"\n... و {len(errors) - 5} خەڵەتی تر"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    def import_members_csv(self):
        """Import members from CSV file"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ ئەندام",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_members_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} ئەندام هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو:\n" + "\n".join(errors[:5])
                    if len(errors) > 5:
                        message += f"\n... و {len(errors) - 5} خەڵەتی تر"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    def export_books_excel(self):
        """Export books to Excel file"""
        file_path = filedialog.asksaveasfilename(
            title="پاشەکەوتکردنی فایلی Excel بۆ پەرتووک",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            try:
                count = self.data_manager.export_books_to_excel(file_path)
                messagebox.showinfo("سەرکەوتوو", f"بە سەرکەوتوویی {count} پەرتووک هەناردەکرا بۆ {file_path}")
            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def export_members_excel(self):
        """Export members to Excel file"""
        file_path = filedialog.asksaveasfilename(
            title="پاشەکەوتکردنی فایلی Excel بۆ ئەندام",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            try:
                count = self.data_manager.export_members_to_excel(file_path)
                messagebox.showinfo("سەرکەوتوو", f"بە سەرکەوتوویی {count} ئەندام هەناردەکرا بۆ {file_path}")
            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def export_complete_data(self):
        """Export complete data to Excel"""
        file_path = filedialog.asksaveasfilename(
            title="پاشەکەوتکردنی ڕاپۆرتی تەواو",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.data_manager.export_complete_report_to_excel(file_path)
                messagebox.showinfo("سەرکەوتوو", f"ڕاپۆرتی تەواو بە سەرکەوتوویی هەناردەکرا بۆ {file_path}")
            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    # Report Methods
    def generate_overdue_report(self):
        """Generate overdue books report"""
        try:
            overdue_books = self.db.get_overdue_books()

            report = f"ڕاپۆرتی پەرتووکی پاشکەوت\n"
            report += f"بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
            report += f"کۆی گشتی: {len(overdue_books)} پەرتووک\n"
            report += "=" * 50 + "\n\n"

            if overdue_books:
                for book in overdue_books:
                    days_overdue = (datetime.now().date() - datetime.strptime(book[4], '%Y-%m-%d').date()).days
                    fine = days_overdue * 250
                    report += f"پەرتووک: {book[0]}\n"
                    report += f"نووسەر: {book[1]}\n"
                    report += f"ئەندام: {book[2]} ({book[3]})\n"
                    report += f"بەرواری گەڕاندنەوە: {book[4]}\n"
                    report += f"ڕۆژی پاشکەوت: {days_overdue}\n"
                    report += f"جەریمە: {fine} دینار\n"
                    report += "-" * 30 + "\n"
            else:
                report += "هیچ پەرتووکێکی پاشکەوت نییە.\n"

            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_active_members_report(self):
        """Generate active members report"""
        try:
            # Get members with borrowed books
            query = '''
                SELECT DISTINCT m.member_id, m.name, m.class_grade, COUNT(t.id) as borrowed_count
                FROM members m
                JOIN transactions t ON m.member_id = t.member_id
                WHERE t.transaction_type = 'borrow' AND t.return_date IS NULL
                GROUP BY m.member_id, m.name, m.class_grade
                ORDER BY borrowed_count DESC
            '''
            active_members = self.db.execute_query(query, fetch=True)

            report = f"ڕاپۆرتی ئەندامی چالاک\n"
            report += f"بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
            report += f"کۆی گشتی: {len(active_members)} ئەندام\n"
            report += "=" * 50 + "\n\n"

            if active_members:
                for member in active_members:
                    report += f"ئەندام: {member[1]}\n"
                    report += f"ژمارە: {member[0]}\n"
                    report += f"پۆل: {member[2] or 'نەدیارکراو'}\n"
                    report += f"پەرتووکی خواستراو: {member[3]}\n"
                    report += "-" * 30 + "\n"
            else:
                report += "هیچ ئەندامێکی چالاک نییە.\n"

            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_statistics_report(self):
        """Generate statistics report"""
        try:
            stats = self.db.get_statistics()

            report = f"ڕاپۆرتی ئاماری گشتی\n"
            report += f"بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
            report += "=" * 50 + "\n\n"

            report += "پەرتووک:\n"
            report += f"  کۆی گشتی: {stats['total_books']}\n"
            report += f"  بەردەست: {stats['available_books']}\n"
            report += f"  خواستراو: {stats['borrowed_books']}\n"
            report += f"  پاشکەوت: {stats['overdue_books']}\n\n"

            report += "ئەندام:\n"
            report += f"  کۆی گشتی: {stats['total_members']}\n"
            report += f"  چالاک: {stats['active_members']}\n\n"

            # Calculate percentages
            if stats['total_books'] > 0:
                available_percent = (stats['available_books'] / stats['total_books']) * 100
                borrowed_percent = (stats['borrowed_books'] / stats['total_books']) * 100
                report += "ڕێژەکان:\n"
                report += f"  پەرتووکی بەردەست: {available_percent:.1f}%\n"
                report += f"  پەرتووکی خواستراو: {borrowed_percent:.1f}%\n"

            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    # Generation Methods
    def generate_member_cards(self):
        """Generate member ID cards"""
        try:
            result = messagebox.askyesno("دڵنیایی", "ئایا دەتەوێت کارتی ئەندامەتی بۆ هەموو ئەندامان دروست بکەیت؟")
            if result:
                card_paths = self.id_generator.generate_all_member_cards()

                if card_paths:
                    html_file = self.id_generator.create_cards_html_page(card_paths)
                    messagebox.showinfo("سەرکەوتوو",
                                      f"بە سەرکەوتوویی {len(card_paths)} کارت دروستکرا\n"
                                      f"فایلی HTML: {html_file}")

                    # Ask to open HTML file
                    if messagebox.askyesno("کردنەوە", "ئایا دەتەوێت فایلی HTML بکەیتەوە؟"):
                        webbrowser.open(html_file)
                else:
                    messagebox.showwarning("ئاگاداری", "هیچ کارتێک دروست نەکرا")

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی کارت: {str(e)}")

    def generate_book_labels(self):
        """Generate book labels"""
        try:
            books = self.db.get_all_books()
            if not books:
                messagebox.showwarning("ئاگاداری", "هیچ پەرتووکێک نییە")
                return

            result = messagebox.askyesno("دڵنیایی", f"ئایا دەتەوێت لیبڵ بۆ هەموو {len(books)} پەرتووک دروست بکەیت؟")
            if result:
                generated_count = 0
                for book in books:
                    try:
                        self.id_generator.generate_book_label(book[1])  # barcode
                        generated_count += 1
                    except Exception as e:
                        print(f"Error generating label for book {book[1]}: {e}")

                messagebox.showinfo("سەرکەوتوو", f"بە سەرکەوتوویی {generated_count} لیبڵ دروستکرا")

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی لیبڵ: {str(e)}")

    def backup_data(self):
        """Create data backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = os.path.join(BACKUP_FOLDER, f"library_backup_{timestamp}")

            # Create backup archive
            shutil.make_archive(backup_filename, 'zip', root_dir='.')

            messagebox.showinfo("سەرکەوتوو", f"یەدەگ بە سەرکەوتوویی دروستکرا:\n{backup_filename}.zip")

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی یەدەگ: {str(e)}")

    def run(self):
        """Run the application"""
        self.root.mainloop()

# ==============================================================================
# Main Execution
# ==============================================================================

if __name__ == "__main__":
    try:
        app = LibraryManagementApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردنی بەرنامە: {str(e)}")
