# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Advanced Modern Library Management System
#                   (Version 7.0 - Multi-Language & Advanced Features)
# ==============================================================================

import customtkinter as ctk
from tkinter import messagebox, filedialog, ttk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import sqlite3
import pandas as pd
import csv
import os
import shutil
from datetime import datetime, timedelta
import barcode
from barcode.writer import ImageWriter
import qrcode
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import json
import webbrowser
import tempfile
from tkcalendar import DateEntry
import threading
import zipfile
import base64
from io import BytesIO
import uuid

# ==============================================================================
# Configuration and Language System
# ==============================================================================

# Configuration file
CONFIG_FILE = 'library_config.json'

# Default configuration
DEFAULT_CONFIG = {
    'language': 'kurdish',
    'appearance_mode': 'light',
    'color_theme': 'blue',
    'school_info': {
        'name': 'قوتابخانا ژین یا بنەرەت',
        'librarian': 'سگڤان خالد نبی',
        'principal': 'أركان خضر طه',
        'address': 'باڤێ - هەرێما کوردستانێ',
        'phone': '+964 ************',
        'email': '<EMAIL>',
        'website': 'www.zhinschool.edu.krd'
    },
    'library_settings': {
        'loan_period_days': 14,
        'max_books_per_student': 3,
        'fine_per_day': 250,
        'currency': 'IQD',
        'working_hours': '8:00 AM - 4:00 PM',
        'working_days': 'Sunday - Thursday'
    },
    'logo_files': {
        'kurdistan': 'لۆگۆیا حکومەتا هەرێما کوردستانێ.png',
        'education': 'لۆگۆیا وەزارەتا پەروەردەیێ.png',
        'school': 'لۆگۆیا قوتابخانا ژین.png'
    },
    'folders': {
        'reports': 'reports',
        'backup': 'backup',
        'barcodes': 'member_barcodes',
        'assets': 'assets',
        'covers': 'covers',
        'exports': 'exports',
        'imports': 'imports',
        'photos': 'photos'
    }
}

# Multi-language support
LANGUAGES = {
    'kurdish': {
        'name': 'کوردی',
        'code': 'ku',
        'rtl': True,
        'font': 'Arial Unicode MS'
    },
    'arabic': {
        'name': 'العربية',
        'code': 'ar',
        'rtl': True,
        'font': 'Arial Unicode MS'
    },
    'english': {
        'name': 'English',
        'code': 'en',
        'rtl': False,
        'font': 'Arial'
    }
}

# Language strings
TRANSLATIONS = {
    'kurdish': {
        # Main interface
        'app_title': 'زانکۆشکا ژین - سیستەمی پەرتووکخانە',
        'dashboard': 'سەرەکی',
        'circulation': 'خواستن/گەڕاندنەوە',
        'books': 'پەرتووک',
        'members': 'ئەندامان',
        'reports': 'ڕاپۆرت',
        'settings': 'ڕێکخستن',

        # Dashboard
        'total_books': 'کۆی پەرتووک',
        'available_books': 'پەرتووکی بەردەست',
        'borrowed_books': 'پەرتووکی خواستراو',
        'overdue_books': 'پەرتووکی پاشکەوت',
        'recent_activity': 'چالاکی نوێ',

        # Forms
        'add_book': 'پەرتووکی نوێ زیادبکە',
        'add_member': 'ئەندامی نوێ زیادبکە',
        'barcode': 'بارکۆد',
        'title': 'ناونیشان',
        'author': 'نووسەر',
        'category': 'جۆر',
        'publisher': 'دەرچوون',
        'publication_year': 'ساڵی دەرچوون',
        'isbn': 'ISBN',
        'pages': 'لاپەڕە',
        'language': 'زمان',
        'summary': 'پوختە',
        'location': 'شوێن',

        # Member fields
        'member_id': 'ژمارەی ئەندام',
        'name': 'ناو',
        'class_grade': 'پۆل',
        'national_id': 'ژمارەی ناسنامە',
        'phone': 'تەلەفۆن',
        'guardian_name': 'ناوی سەرپەرشتیار',
        'guardian_phone': 'تەلەفۆنی سەرپەرشتیار',
        'address': 'ناونیشان',
        'email': 'ئیمەیڵ',

        # Actions
        'save': 'پاشەکەوتکردن',
        'cancel': 'پاشگەزبوونەوە',
        'search': 'گەڕان',
        'import': 'هاوردەکردن',
        'export': 'هەناردەکردن',
        'backup': 'یەدەگ گرتن',
        'restore': 'گەڕاندنەوە',

        # Messages
        'success': 'سەرکەوتوو',
        'error': 'خەڵەتی',
        'warning': 'ئاگاداری',
        'info': 'زانیاری',

        # Placeholders
        'enter_barcode': 'بارکۆد داخڵ بکە',
        'enter_title': 'ناونیشان داخڵ بکە',
        'enter_author': 'ناوی نووسەر داخڵ بکە',
        'enter_member_id': 'ژمارەی ئەندام داخڵ بکە',
        'enter_name': 'ناو داخڵ بکە',
        'search_books': 'گەڕان بە ناونیشان، نووسەر، یان بارکۆد...',
        'search_members': 'گەڕان بە ناو، ژمارەی ئەندام، یان پۆل...'
    },

    'arabic': {
        # Main interface
        'app_title': 'مدرسة جين - نظام إدارة المكتبة',
        'dashboard': 'الرئيسية',
        'circulation': 'الإعارة/الإرجاع',
        'books': 'الكتب',
        'members': 'الأعضاء',
        'reports': 'التقارير',
        'settings': 'الإعدادات',

        # Dashboard
        'total_books': 'إجمالي الكتب',
        'available_books': 'الكتب المتاحة',
        'borrowed_books': 'الكتب المستعارة',
        'overdue_books': 'الكتب المتأخرة',
        'recent_activity': 'النشاط الحديث',

        # Forms
        'add_book': 'إضافة كتاب جديد',
        'add_member': 'إضافة عضو جديد',
        'barcode': 'الرمز الشريطي',
        'title': 'العنوان',
        'author': 'المؤلف',
        'category': 'الفئة',
        'publisher': 'الناشر',
        'publication_year': 'سنة النشر',
        'isbn': 'الرقم المعياري',
        'pages': 'الصفحات',
        'language': 'اللغة',
        'summary': 'الملخص',
        'location': 'الموقع',

        # Member fields
        'member_id': 'رقم العضو',
        'name': 'الاسم',
        'class_grade': 'الصف',
        'national_id': 'رقم الهوية',
        'phone': 'الهاتف',
        'guardian_name': 'اسم ولي الأمر',
        'guardian_phone': 'هاتف ولي الأمر',
        'address': 'العنوان',
        'email': 'البريد الإلكتروني',

        # Actions
        'save': 'حفظ',
        'cancel': 'إلغاء',
        'search': 'بحث',
        'import': 'استيراد',
        'export': 'تصدير',
        'backup': 'نسخ احتياطي',
        'restore': 'استعادة',

        # Messages
        'success': 'نجح',
        'error': 'خطأ',
        'warning': 'تحذير',
        'info': 'معلومات',

        # Placeholders
        'enter_barcode': 'أدخل الرمز الشريطي',
        'enter_title': 'أدخل العنوان',
        'enter_author': 'أدخل اسم المؤلف',
        'enter_member_id': 'أدخل رقم العضو',
        'enter_name': 'أدخل الاسم',
        'search_books': 'البحث بالعنوان أو المؤلف أو الرمز الشريطي...',
        'search_members': 'البحث بالاسم أو رقم العضو أو الصف...'
    },

    'english': {
        # Main interface
        'app_title': 'Zhin School - Library Management System',
        'dashboard': 'Dashboard',
        'circulation': 'Circulation',
        'books': 'Books',
        'members': 'Members',
        'reports': 'Reports',
        'settings': 'Settings',

        # Dashboard
        'total_books': 'Total Books',
        'available_books': 'Available Books',
        'borrowed_books': 'Borrowed Books',
        'overdue_books': 'Overdue Books',
        'recent_activity': 'Recent Activity',

        # Forms
        'add_book': 'Add New Book',
        'add_member': 'Add New Member',
        'barcode': 'Barcode',
        'title': 'Title',
        'author': 'Author',
        'category': 'Category',
        'publisher': 'Publisher',
        'publication_year': 'Publication Year',
        'isbn': 'ISBN',
        'pages': 'Pages',
        'language': 'Language',
        'summary': 'Summary',
        'location': 'Location',

        # Member fields
        'member_id': 'Member ID',
        'name': 'Name',
        'class_grade': 'Class/Grade',
        'national_id': 'National ID',
        'phone': 'Phone',
        'guardian_name': 'Guardian Name',
        'guardian_phone': 'Guardian Phone',
        'address': 'Address',
        'email': 'Email',

        # Actions
        'save': 'Save',
        'cancel': 'Cancel',
        'search': 'Search',
        'import': 'Import',
        'export': 'Export',
        'backup': 'Backup',
        'restore': 'Restore',

        # Messages
        'success': 'Success',
        'error': 'Error',
        'warning': 'Warning',
        'info': 'Information',

        # Placeholders
        'enter_barcode': 'Enter barcode',
        'enter_title': 'Enter title',
        'enter_author': 'Enter author name',
        'enter_member_id': 'Enter member ID',
        'enter_name': 'Enter name',
        'search_books': 'Search by title, author, or barcode...',
        'search_members': 'Search by name, member ID, or class...'
    }
}

# Set appearance mode and color theme
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

# Modern color scheme
COLORS = {
    'primary': '#1f538d',
    'secondary': '#14375e',
    'accent': '#ffa500',
    'success': '#28a745',
    'warning': '#ffc107',
    'danger': '#dc3545',
    'light': '#f8f9fa',
    'dark': '#343a40',
    'white': '#ffffff',
    'gray': '#6c757d'
}

# ==============================================================================
# Configuration Manager
# ==============================================================================

class ConfigManager:
    def __init__(self):
        self.config = self.load_config()
        self.ensure_folders()

    def load_config(self):
        """Load configuration from JSON file"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Merge with defaults for any missing keys
                return self.merge_config(DEFAULT_CONFIG, config)
            else:
                return DEFAULT_CONFIG.copy()
        except Exception as e:
            print(f"Error loading config: {e}")
            return DEFAULT_CONFIG.copy()

    def save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")

    def merge_config(self, default, user):
        """Merge user config with default config"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_config(result[key], value)
            else:
                result[key] = value
        return result

    def get(self, key, default=None):
        """Get configuration value"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

    def set(self, key, value):
        """Set configuration value"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()

    def ensure_folders(self):
        """Create necessary folders"""
        folders = self.get('folders', {})
        for folder_name, folder_path in folders.items():
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)

# ==============================================================================
# Enhanced Database Manager
# ==============================================================================

class EnhancedDatabaseManager:
    def __init__(self, config_manager):
        self.config = config_manager
        self.db_file = 'library_system.db'
        self.init_database()

    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        # Books table with additional fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS books (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                author TEXT NOT NULL,
                ddc_number TEXT,
                author_code TEXT,
                call_number TEXT,
                isbn TEXT,
                category TEXT,
                publisher TEXT,
                publication_year INTEGER,
                pages INTEGER,
                language TEXT DEFAULT 'کوردی',
                summary TEXT,
                status TEXT DEFAULT 'Available',
                location TEXT,
                cover_image TEXT,
                acquisition_date DATE DEFAULT CURRENT_DATE,
                price REAL,
                condition_status TEXT DEFAULT 'Good',
                notes TEXT,
                date_added DATE DEFAULT CURRENT_DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Members table with additional fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                class_grade TEXT,
                national_id TEXT,
                phone TEXT,
                guardian_name TEXT,
                guardian_phone TEXT,
                address TEXT,
                email TEXT,
                date_of_birth DATE,
                gender TEXT,
                photo_path TEXT,
                registration_date DATE DEFAULT CURRENT_DATE,
                expiry_date DATE,
                status TEXT DEFAULT 'Active',
                membership_type TEXT DEFAULT 'Student',
                emergency_contact TEXT,
                emergency_phone TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Enhanced transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_uuid TEXT UNIQUE DEFAULT (lower(hex(randomblob(16)))),
                book_barcode TEXT NOT NULL,
                member_id TEXT NOT NULL,
                transaction_type TEXT NOT NULL,
                transaction_date DATE DEFAULT CURRENT_DATE,
                due_date DATE,
                return_date DATE,
                fine_amount REAL DEFAULT 0,
                fine_paid BOOLEAN DEFAULT FALSE,
                renewal_count INTEGER DEFAULT 0,
                notes TEXT,
                librarian_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (book_barcode) REFERENCES books (barcode),
                FOREIGN KEY (member_id) REFERENCES members (member_id)
            )
        ''')

        # Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT,
                category TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Activity log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                entity_type TEXT NOT NULL,
                entity_id TEXT NOT NULL,
                details TEXT,
                user_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def execute_query(self, query, params=None, fetch=False):
        """Execute a database query"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if fetch:
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                conn.close()
                return cursor.rowcount
        except Exception as e:
            conn.close()
            raise e

    def log_activity(self, action, entity_type, entity_id, details=None, user_id=None):
        """Log activity to database"""
        try:
            query = '''
                INSERT INTO activity_log (action, entity_type, entity_id, details, user_id)
                VALUES (?, ?, ?, ?, ?)
            '''
            self.execute_query(query, (action, entity_type, entity_id, details, user_id))
        except Exception as e:
            print(f"Error logging activity: {e}")

    def get_all_books(self):
        """Get all books from database"""
        query = "SELECT * FROM books ORDER BY title"
        return self.execute_query(query, fetch=True)

    def get_all_members(self):
        """Get all members from database"""
        query = "SELECT * FROM members ORDER BY name"
        return self.execute_query(query, fetch=True)

    def add_book(self, book_data):
        """Add a new book to database"""
        query = '''
            INSERT INTO books (barcode, title, author, ddc_number, author_code,
                             call_number, isbn, category, publisher, publication_year,
                             pages, language, summary, location, cover_image, price,
                             condition_status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        result = self.execute_query(query, book_data)
        self.log_activity('ADD', 'BOOK', book_data[0], f'Added book: {book_data[1]}')
        return result

    def add_member(self, member_data):
        """Add a new member to database"""
        query = '''
            INSERT INTO members (member_id, name, class_grade, national_id, phone,
                               guardian_name, guardian_phone, address, email, date_of_birth,
                               gender, photo_path, expiry_date, membership_type,
                               emergency_contact, emergency_phone, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        result = self.execute_query(query, member_data)
        self.log_activity('ADD', 'MEMBER', member_data[0], f'Added member: {member_data[1]}')
        return result

    def find_member_by_id(self, member_id):
        """Find member by ID"""
        query = "SELECT * FROM members WHERE member_id = ?"
        result = self.execute_query(query, (member_id,), fetch=True)
        return result[0] if result else None

# ==============================================================================
# Advanced Export Dialog
# ==============================================================================

class AdvancedExportDialog(ctk.CTkToplevel):
    def __init__(self, parent, export_type="books", config_manager=None):
        super().__init__(parent)

        self.export_type = export_type
        self.parent = parent
        self.config = config_manager
        self.result = None
        self.current_lang = self.config.get('language', 'kurdish') if self.config else 'kurdish'

        # Get translations
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])

        self.title(f"{self.t.get('export', 'Export')} - {export_type}")
        self.geometry("700x600")
        self.transient(parent)
        self.grab_set()

        # Center the dialog
        self.center_window()

        self.create_widgets()
    
    def center_window(self):
        """Center the dialog on parent window"""
        self.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2)) - (self.winfo_width() // 2)
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2)) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main scrollable frame
        main_frame = ctk.CTkScrollableFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=f"{self.t.get('export', 'Export')} {self.export_type}",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Export format selection
        format_frame = ctk.CTkFrame(main_frame)
        format_frame.pack(fill="x", pady=(0, 15))

        ctk.CTkLabel(format_frame, text=f"{self.t.get('file_format', 'File Format')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.format_var = ctk.StringVar(value="excel")
        format_radio_frame = ctk.CTkFrame(format_frame)
        format_radio_frame.pack(fill="x", padx=15, pady=(0, 15))

        ctk.CTkRadioButton(format_radio_frame, text="Excel (.xlsx)", variable=self.format_var, value="excel").pack(side="left", padx=(0, 20))
        ctk.CTkRadioButton(format_radio_frame, text="CSV (.csv)", variable=self.format_var, value="csv").pack(side="left", padx=(0, 20))
        ctk.CTkRadioButton(format_radio_frame, text="JSON (.json)", variable=self.format_var, value="json").pack(side="left")

        # Language selection for export
        lang_frame = ctk.CTkFrame(main_frame)
        lang_frame.pack(fill="x", pady=(0, 15))

        ctk.CTkLabel(lang_frame, text=f"{self.t.get('export_language', 'Export Language')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.export_lang_var = ctk.StringVar(value="current")
        lang_radio_frame = ctk.CTkFrame(lang_frame)
        lang_radio_frame.pack(fill="x", padx=15, pady=(0, 15))

        ctk.CTkRadioButton(lang_radio_frame, text=f"Current ({LANGUAGES[self.current_lang]['name']})",
                          variable=self.export_lang_var, value="current").pack(side="left", padx=(0, 15))
        ctk.CTkRadioButton(lang_radio_frame, text="All Languages",
                          variable=self.export_lang_var, value="all").pack(side="left")
        
        # Sample data generation
        sample_frame = ctk.CTkFrame(main_frame)
        sample_frame.pack(fill="x", pady=(0, 15))

        ctk.CTkLabel(sample_frame, text=f"{self.t.get('sample_data', 'Sample Data')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.generate_sample = ctk.BooleanVar()
        sample_check = ctk.CTkCheckBox(sample_frame, text=f"{self.t.get('generate_sample', 'Generate Sample Data')}",
                                     variable=self.generate_sample)
        sample_check.pack(anchor="w", padx=15, pady=(0, 10))

        # Sample data options
        sample_options_frame = ctk.CTkFrame(sample_frame)
        sample_options_frame.pack(fill="x", padx=15, pady=(0, 15))

        ctk.CTkLabel(sample_options_frame, text=f"{self.t.get('sample_count', 'Sample Count')}:").pack(side="left", padx=(10, 5))
        self.sample_count_var = ctk.StringVar(value="50")
        sample_count_entry = ctk.CTkEntry(sample_options_frame, textvariable=self.sample_count_var, width=80)
        sample_count_entry.pack(side="left", padx=(5, 10))

        # Date range selection
        date_frame = ctk.CTkFrame(main_frame)
        date_frame.pack(fill="x", pady=(0, 15))

        ctk.CTkLabel(date_frame, text=f"{self.t.get('date_range', 'Date Range')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.use_date_range = ctk.BooleanVar()
        date_check = ctk.CTkCheckBox(date_frame, text=f"{self.t.get('use_date_range', 'Use Date Range')}",
                                   variable=self.use_date_range, command=self.toggle_date_range)
        date_check.pack(anchor="w", padx=15, pady=(0, 10))
        
        # Date range inputs
        self.date_range_frame = ctk.CTkFrame(date_frame)
        self.date_range_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        # From date
        from_frame = ctk.CTkFrame(self.date_range_frame)
        from_frame.pack(fill="x", pady=(10, 5))

        ctk.CTkLabel(from_frame, text=f"{self.t.get('from_date', 'From Date')}:").pack(side="left", padx=(10, 5))
        self.from_date = DateEntry(from_frame, width=12, background='darkblue',
                                  foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.from_date.pack(side="left", padx=(5, 10))

        # To date
        to_frame = ctk.CTkFrame(self.date_range_frame)
        to_frame.pack(fill="x", pady=(5, 10))

        ctk.CTkLabel(to_frame, text=f"{self.t.get('to_date', 'To Date')}:").pack(side="left", padx=(10, 5))
        self.to_date = DateEntry(to_frame, width=12, background='darkblue',
                                foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.to_date.pack(side="left", padx=(5, 10))
        
        # Initially disable date range
        self.toggle_date_range()
        
        # Filter options
        filter_frame = ctk.CTkFrame(main_frame)
        filter_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(filter_frame, text="فلتەرەکان:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        # Status filter
        if self.export_type == "books":
            self.status_var = ctk.StringVar(value="all")
            status_frame = ctk.CTkFrame(filter_frame)
            status_frame.pack(fill="x", padx=15, pady=(0, 10))
            
            ctk.CTkLabel(status_frame, text="دۆخ:").pack(side="left", padx=(10, 5))
            status_menu = ctk.CTkOptionMenu(status_frame, variable=self.status_var,
                                          values=["all", "Available", "Borrowed"])
            status_menu.pack(side="left", padx=(5, 10))
        
        # Include additional data
        options_frame = ctk.CTkFrame(filter_frame)
        options_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.include_stats = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(options_frame, text="لەگەڵ ئاماری گشتی", variable=self.include_stats).pack(anchor="w", padx=10, pady=5)
        
        self.include_summary = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(options_frame, text="لەگەڵ پوختەی ڕاپۆرت", variable=self.include_summary).pack(anchor="w", padx=10, pady=5)
        
        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", pady=(20, 0))
        
        ctk.CTkButton(
            button_frame,
            text="هەناردەکردن",
            command=self.export_data,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="right", padx=(10, 15), pady=15)
        
        ctk.CTkButton(
            button_frame,
            text="پاشگەزبوونەوە",
            command=self.destroy,
            fg_color=COLORS['gray'],
            hover_color=COLORS['dark']
        ).pack(side="right", padx=(15, 0), pady=15)
    
    def toggle_date_range(self):
        """Toggle date range inputs"""
        if self.use_date_range.get():
            for widget in self.date_range_frame.winfo_children():
                for child in widget.winfo_children():
                    if hasattr(child, 'configure'):
                        child.configure(state="normal")
        else:
            for widget in self.date_range_frame.winfo_children():
                for child in widget.winfo_children():
                    if hasattr(child, 'configure') and child != self.date_range_frame:
                        try:
                            child.configure(state="disabled")
                        except:
                            pass
    
    def export_data(self):
        """Export data with selected options"""
        try:
            # Get file path
            if self.format_var.get() == "excel":
                file_path = filedialog.asksaveasfilename(
                    title="پاشەکەوتکردنی فایل",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
            else:
                file_path = filedialog.asksaveasfilename(
                    title="پاشەکەوتکردنی فایل",
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
            
            if not file_path:
                return
            
            # Prepare export options
            options = {
                'format': self.format_var.get(),
                'use_date_range': self.use_date_range.get(),
                'from_date': self.from_date.get_date() if self.use_date_range.get() else None,
                'to_date': self.to_date.get_date() if self.use_date_range.get() else None,
                'include_stats': self.include_stats.get(),
                'include_summary': self.include_summary.get(),
                'file_path': file_path
            }
            
            if hasattr(self, 'status_var'):
                options['status_filter'] = self.status_var.get()
            
            self.result = options
            self.destroy()

        except Exception as e:
            messagebox.showerror(self.t.get('error', 'Error'), f"{self.t.get('export_error', 'Export Error')}: {str(e)}")

# ==============================================================================
# Enhanced ID Card Generator
# ==============================================================================

class EnhancedIDCardGenerator:
    def __init__(self, db_manager, config_manager):
        self.db = db_manager
        self.config = config_manager
        self.current_lang = self.config.get('language', 'kurdish')
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])

        # Card dimensions
        self.card_width = 600
        self.card_height = 380
        self.label_width = 400
        self.label_height = 300

    def load_logo(self, logo_path, size=(60, 60)):
        """Load and resize logo image"""
        try:
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize(size, Image.Resampling.LANCZOS)
                return img
            else:
                # Create placeholder if logo not found
                img = Image.new('RGBA', size, (200, 200, 200, 255))
                draw = ImageDraw.Draw(img)
                draw.text((size[0]//2, size[1]//2), "LOGO", fill=(100, 100, 100), anchor="mm")
                return img
        except Exception as e:
            print(f"Error loading logo {logo_path}: {e}")
            img = Image.new('RGBA', size, (200, 200, 200, 255))
            return img

    def generate_member_id_card(self, member_id, output_path=None):
        """Generate enhanced membership ID card"""
        try:
            member = self.db.find_member_by_id(member_id)
            if not member:
                raise ValueError(self.t.get('member_not_found', 'Member not found'))

            # Create card image with gradient background
            card = Image.new('RGB', (self.card_width, self.card_height), '#ffffff')
            draw = ImageDraw.Draw(card)

            # Add gradient background
            for y in range(self.card_height):
                color_value = int(255 - (y / self.card_height) * 30)
                color = (color_value, color_value, 255)
                draw.line([(0, y), (self.card_width, y)], fill=color)

            # Load logos
            logo_files = self.config.get('logo_files', {})
            kurdistan_logo = self.load_logo(logo_files.get('kurdistan', ''), (50, 50))
            education_logo = self.load_logo(logo_files.get('education', ''), (50, 50))
            school_logo = self.load_logo(logo_files.get('school', ''), (50, 50))

            # Paste logos at top
            card.paste(school_logo, (50, 20), school_logo if school_logo.mode == 'RGBA' else None)
            card.paste(education_logo, (275, 20), education_logo if education_logo.mode == 'RGBA' else None)
            card.paste(kurdistan_logo, (500, 20), kurdistan_logo if kurdistan_logo.mode == 'RGBA' else None)

            # Load fonts
            try:
                font_large = ImageFont.truetype("arial.ttf", 18)
                font_medium = ImageFont.truetype("arial.ttf", 14)
                font_small = ImageFont.truetype("arial.ttf", 11)
                font_tiny = ImageFont.truetype("arial.ttf", 9)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
                font_tiny = ImageFont.load_default()

            # School name
            school_name = self.config.get('school_info.name', 'Library')
            draw.text((300, 85), school_name, fill='#1f538d', font=font_large, anchor="mm")

            # Decorative line
            draw.rectangle([(50, 105), (550, 107)], fill='#ffa500')

            # Card title
            card_title = self.t.get('membership_card', 'Membership Card')
            draw.text((300, 125), card_title, fill='#14375e', font=font_medium, anchor="mm")

            # Member photo
            photo_box = (80, 150, 180, 250)
            if member[12] and os.path.exists(member[12]):  # photo_path
                try:
                    photo = Image.open(member[12])
                    photo = photo.resize((100, 100), Image.Resampling.LANCZOS)
                    card.paste(photo, (80, 150))
                except:
                    draw.rectangle(photo_box, outline='#1f538d', width=2, fill='#f0f0f0')
                    draw.text((130, 200), self.t.get('photo', 'Photo'), fill='gray', font=font_medium, anchor="mm")
            else:
                draw.rectangle(photo_box, outline='#1f538d', width=2, fill='#f0f0f0')
                draw.text((130, 200), self.t.get('photo', 'Photo'), fill='gray', font=font_medium, anchor="mm")

            # Member information with better formatting
            info_x = 200
            info_y = 160
            line_height = 22

            member_info = [
                f"{self.t.get('name', 'Name')}: {member[2]}",
                f"{self.t.get('class_grade', 'Class')}: {member[3] or self.t.get('not_specified', 'Not Specified')}",
                f"{self.t.get('member_id', 'Member ID')}: {member[1]}",
                f"{self.t.get('expiry_date', 'Expires')}: {member[14] or 'N/A'}"
            ]

            for i, info in enumerate(member_info):
                # Add background for text
                text_bbox = draw.textbbox((info_x, info_y + i * line_height), info, font=font_small)
                draw.rectangle([text_bbox[0]-5, text_bbox[1]-2, text_bbox[2]+5, text_bbox[3]+2],
                             fill='rgba(255,255,255,200)')
                draw.text((info_x, info_y + i * line_height), info, fill='#1f538d', font=font_small)

            # Generate QR code with member info
            qr_data = json.dumps({
                'member_id': member[1],
                'name': member[2],
                'class': member[3],
                'type': 'library_member'
            })

            qr = qrcode.QRCode(version=1, box_size=3, border=2)
            qr.add_data(qr_data)
            qr.make(fit=True)

            qr_img = qr.make_image(fill_color="black", back_color="white")
            qr_img = qr_img.resize((80, 80), Image.Resampling.LANCZOS)
            card.paste(qr_img, (460, 180))

            # Footer with rules
            draw.rectangle([(50, 320), (550, 322)], fill='#ffa500')

            footer_text = [
                f"• {self.t.get('loan_period', 'Loan Period')}: {self.config.get('library_settings.loan_period_days', 14)} {self.t.get('days', 'days')}",
                f"• {self.t.get('keep_card_safe', 'Keep this card safe and clean')}"
            ]

            for i, text in enumerate(footer_text):
                draw.text((60, 330 + i * 12), text, fill='#14375e', font=font_tiny)

            # Card ID and date
            card_id = f"ID: {member[1]} | {datetime.now().strftime('%Y-%m-%d')}"
            draw.text((550, 360), card_id, fill='#6c757d', font=font_tiny, anchor="ra")

            # Save card
            if not output_path:
                exports_folder = self.config.get('folders.exports', 'exports')
                output_path = os.path.join(exports_folder, f"member_card_{member_id}.png")

            card.save(output_path, quality=95, optimize=True)
            return output_path

        except Exception as e:
            raise Exception(f"Error generating member ID card: {str(e)}")

    def create_beautiful_html_cards(self, card_paths):
        """Create beautiful HTML page with all member cards"""
        try:
            school_info = self.config.get('school_info', {})

            html_content = f"""
<!DOCTYPE html>
<html lang="{LANGUAGES[self.current_lang]['code']}" dir="{'rtl' if LANGUAGES[self.current_lang]['rtl'] else 'ltr'}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.t.get('membership_cards', 'Membership Cards')} - {school_info.get('name', 'Library')}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}

        .header {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }}

        .header h1 {{
            color: #1f538d;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }}

        .header p {{
            color: #666;
            font-size: 1.2em;
            margin-bottom: 5px;
        }}

        .stats {{
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }}

        .stat-item {{
            background: linear-gradient(45deg, #1f538d, #14375e);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(31, 83, 141, 0.3);
        }}

        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            display: block;
        }}

        .cards-container {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            max-width: 1400px;
            margin: 0 auto;
        }}

        .card-wrapper {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}

        .card-wrapper:hover {{
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
        }}

        .card-wrapper img {{
            width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }}

        .card-info {{
            margin-top: 15px;
            text-align: center;
        }}

        .card-title {{
            font-weight: bold;
            color: #1f538d;
            font-size: 1.1em;
            margin-bottom: 5px;
        }}

        .card-subtitle {{
            color: #666;
            font-size: 0.9em;
        }}

        .footer {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }}

        .print-btn {{
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
        }}

        .print-btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }}

        @media print {{
            body {{
                background: white;
                padding: 0;
            }}

            .header, .footer {{
                background: white;
                box-shadow: none;
                border: 1px solid #ddd;
            }}

            .card-wrapper {{
                background: white;
                box-shadow: none;
                border: 1px solid #ddd;
                break-inside: avoid;
                margin-bottom: 20px;
            }}

            .print-btn {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{school_info.get('name', 'Library System')}</h1>
        <p>{self.t.get('membership_cards', 'Membership Cards')}</p>
        <p>{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

        <div class="stats">
            <div class="stat-item">
                <span class="stat-number">{len(card_paths)}</span>
                <span>{self.t.get('total_cards', 'Total Cards')}</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{datetime.now().strftime('%Y')}</span>
                <span>{self.t.get('academic_year', 'Academic Year')}</span>
            </div>
        </div>

        <button class="print-btn" onclick="window.print()">{self.t.get('print_cards', 'Print Cards')}</button>
        <button class="print-btn" onclick="downloadAll()">{self.t.get('download_all', 'Download All')}</button>
    </div>

    <div class="cards-container">
"""

            for i, card_path in enumerate(card_paths):
                if os.path.exists(card_path):
                    card_name = os.path.basename(card_path)
                    member_id = card_name.replace('member_card_', '').replace('.png', '')

                    # Convert image to base64 for embedding
                    with open(card_path, 'rb') as img_file:
                        img_data = base64.b64encode(img_file.read()).decode()

                    html_content += f"""
        <div class="card-wrapper">
            <img src="data:image/png;base64,{img_data}" alt="{card_name}">
            <div class="card-info">
                <div class="card-title">{self.t.get('member_id', 'Member ID')}: {member_id}</div>
                <div class="card-subtitle">{self.t.get('card_number', 'Card')} #{i+1:03d}</div>
            </div>
        </div>
"""

            html_content += f"""
    </div>

    <div class="footer">
        <p>&copy; {datetime.now().year} {school_info.get('name', 'Library System')}</p>
        <p>{self.t.get('contact', 'Contact')}: {school_info.get('phone', '')} | {school_info.get('email', '')}</p>
        <p>{self.t.get('address', 'Address')}: {school_info.get('address', '')}</p>
    </div>

    <script>
        function downloadAll() {{
            const cards = document.querySelectorAll('.card-wrapper img');
            cards.forEach((img, index) => {{
                const link = document.createElement('a');
                link.download = `member_card_${{index + 1:03d}}.png`;
                link.href = img.src;
                link.click();
            }});
        }}
    </script>
</body>
</html>
"""

            exports_folder = self.config.get('folders.exports', 'exports')
            html_file_path = os.path.join(exports_folder, "member_cards_beautiful.html")

            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            return html_file_path

        except Exception as e:
            raise Exception(f"Error creating HTML page: {str(e)}")

# ==============================================================================
# Backup and Restore Manager
# ==============================================================================

class BackupRestoreManager:
    def __init__(self, db_manager, config_manager):
        self.db = db_manager
        self.config = config_manager
        self.current_lang = self.config.get('language', 'kurdish')
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])

    def create_full_backup(self, backup_path=None):
        """Create a complete backup of the system"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if not backup_path:
                backup_folder = self.config.get('folders.backup', 'backup')
                backup_path = os.path.join(backup_folder, f"library_backup_{timestamp}.zip")

            # Create temporary directory for backup files
            temp_dir = tempfile.mkdtemp()

            try:
                # Copy database
                db_backup_path = os.path.join(temp_dir, 'library_system.db')
                shutil.copy2(self.db.db_file, db_backup_path)

                # Copy configuration
                config_backup_path = os.path.join(temp_dir, 'library_config.json')
                if os.path.exists(CONFIG_FILE):
                    shutil.copy2(CONFIG_FILE, config_backup_path)

                # Copy assets and photos
                folders_to_backup = ['assets', 'photos', 'covers', 'member_barcodes']
                for folder_name in folders_to_backup:
                    folder_path = self.config.get(f'folders.{folder_name}', folder_name)
                    if os.path.exists(folder_path):
                        backup_folder_path = os.path.join(temp_dir, folder_name)
                        shutil.copytree(folder_path, backup_folder_path)

                # Create backup info
                backup_info = {
                    'backup_date': datetime.now().isoformat(),
                    'system_version': '7.0',
                    'database_version': '1.0',
                    'language': self.current_lang,
                    'school_name': self.config.get('school_info.name', 'Unknown'),
                    'total_books': len(self.db.get_all_books()),
                    'total_members': len(self.db.get_all_members())
                }

                info_path = os.path.join(temp_dir, 'backup_info.json')
                with open(info_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, ensure_ascii=False, indent=4)

                # Create ZIP archive
                with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, temp_dir)
                            zipf.write(file_path, arcname)

                return backup_path, backup_info

            finally:
                # Clean up temporary directory
                shutil.rmtree(temp_dir)

        except Exception as e:
            raise Exception(f"Error creating backup: {str(e)}")

    def restore_from_backup(self, backup_path):
        """Restore system from backup"""
        try:
            if not os.path.exists(backup_path):
                raise ValueError(f"Backup file not found: {backup_path}")

            # Create temporary directory for extraction
            temp_dir = tempfile.mkdtemp()

            try:
                # Extract backup
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)

                # Read backup info
                info_path = os.path.join(temp_dir, 'backup_info.json')
                backup_info = {}
                if os.path.exists(info_path):
                    with open(info_path, 'r', encoding='utf-8') as f:
                        backup_info = json.load(f)

                # Confirm restore
                confirm_msg = f"""
{self.t.get('restore_confirmation', 'Restore Confirmation')}

{self.t.get('backup_date', 'Backup Date')}: {backup_info.get('backup_date', 'Unknown')}
{self.t.get('school_name', 'School')}: {backup_info.get('school_name', 'Unknown')}
{self.t.get('total_books', 'Books')}: {backup_info.get('total_books', 0)}
{self.t.get('total_members', 'Members')}: {backup_info.get('total_members', 0)}

{self.t.get('restore_warning', 'This will replace all current data. Continue?')}
"""

                if not messagebox.askyesno(self.t.get('confirm', 'Confirm'), confirm_msg):
                    return False

                # Backup current data before restore
                current_backup_path = self.create_full_backup()[0]
                print(f"Current data backed up to: {current_backup_path}")

                # Restore database
                db_restore_path = os.path.join(temp_dir, 'library_system.db')
                if os.path.exists(db_restore_path):
                    shutil.copy2(db_restore_path, self.db.db_file)

                # Restore configuration
                config_restore_path = os.path.join(temp_dir, 'library_config.json')
                if os.path.exists(config_restore_path):
                    shutil.copy2(config_restore_path, CONFIG_FILE)
                    # Reload configuration
                    self.config.config = self.config.load_config()

                # Restore folders
                folders_to_restore = ['assets', 'photos', 'covers', 'member_barcodes']
                for folder_name in folders_to_restore:
                    restore_folder_path = os.path.join(temp_dir, folder_name)
                    if os.path.exists(restore_folder_path):
                        target_folder = self.config.get(f'folders.{folder_name}', folder_name)
                        if os.path.exists(target_folder):
                            shutil.rmtree(target_folder)
                        shutil.copytree(restore_folder_path, target_folder)

                return True, backup_info

            finally:
                # Clean up temporary directory
                shutil.rmtree(temp_dir)

        except Exception as e:
            raise Exception(f"Error restoring backup: {str(e)}")

    def export_data_json(self, export_path=None):
        """Export all data to JSON format"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if not export_path:
                exports_folder = self.config.get('folders.exports', 'exports')
                export_path = os.path.join(exports_folder, f"library_data_{timestamp}.json")

            # Collect all data
            export_data = {
                'export_info': {
                    'export_date': datetime.now().isoformat(),
                    'system_version': '7.0',
                    'language': self.current_lang,
                    'school_info': self.config.get('school_info', {})
                },
                'books': [],
                'members': [],
                'transactions': [],
                'settings': self.config.config
            }

            # Get books data
            books = self.db.get_all_books()
            book_columns = ['id', 'barcode', 'title', 'author', 'ddc_number', 'author_code',
                           'call_number', 'isbn', 'category', 'publisher', 'publication_year',
                           'pages', 'language', 'summary', 'status', 'location', 'cover_image',
                           'acquisition_date', 'price', 'condition_status', 'notes',
                           'date_added', 'created_at', 'updated_at']

            for book in books:
                book_dict = dict(zip(book_columns, book))
                export_data['books'].append(book_dict)

            # Get members data
            members = self.db.get_all_members()
            member_columns = ['id', 'member_id', 'name', 'class_grade', 'national_id', 'phone',
                             'guardian_name', 'guardian_phone', 'address', 'email', 'date_of_birth',
                             'gender', 'photo_path', 'registration_date', 'expiry_date', 'status',
                             'membership_type', 'emergency_contact', 'emergency_phone', 'notes',
                             'created_at', 'updated_at']

            for member in members:
                member_dict = dict(zip(member_columns, member))
                export_data['members'].append(member_dict)

            # Get transactions data
            transactions_query = "SELECT * FROM transactions ORDER BY created_at DESC"
            transactions = self.db.execute_query(transactions_query, fetch=True)
            transaction_columns = ['id', 'transaction_uuid', 'book_barcode', 'member_id',
                                 'transaction_type', 'transaction_date', 'due_date', 'return_date',
                                 'fine_amount', 'fine_paid', 'renewal_count', 'notes',
                                 'librarian_id', 'created_at']

            for transaction in transactions:
                transaction_dict = dict(zip(transaction_columns, transaction))
                export_data['transactions'].append(transaction_dict)

            # Save to JSON file
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            return export_path, export_data['export_info']

        except Exception as e:
            raise Exception(f"Error exporting data to JSON: {str(e)}")

# ==============================================================================
# Sample Data Generator
# ==============================================================================

class SampleDataGenerator:
    def __init__(self, config_manager):
        self.config = config_manager
        self.current_lang = self.config.get('language', 'kurdish')

    def generate_sample_books(self, count=50, language='all'):
        """Generate sample books data"""
        sample_books = []

        # Sample data for different languages
        book_samples = {
            'kurdish': [
                ('کوردستان لە مێژوودا', 'د. کەمال مەزهەر', '956.7', 'مێژوو', 'چاپخانەی کوردستان'),
                ('زمانی کوردی', 'پرۆف. گەلاوێژ فەتاح', '491.5', 'زمان', 'دەزگای چاپ و بڵاوکردنەوە'),
                ('ئەدەبیاتی کوردی', 'د. هەژار موکریانی', '891.5', 'ئەدەبیات', 'چاپخانەی ئاراس'),
                ('بیرکاری بۆ منداڵان', 'ئاریان ئەحمەد', '372.7', 'پەروەردە', 'چاپخانەی منداڵان'),
                ('جوگرافیای کوردستان', 'د. ڕەشید سابیر', '915.6', 'جوگرافیا', 'چاپخانەی زانکۆ')
            ],
            'arabic': [
                ('تاريخ الأدب العربي', 'د. شوقي ضيف', '892.7', 'أدب', 'دار المعارف'),
                ('النحو الواضح', 'علي الجارم', '415', 'لغة', 'دار المعارف'),
                ('الرياضيات للمرحلة الابتدائية', 'أحمد محمد', '510', 'رياضيات', 'دار الكتب'),
                ('العلوم الطبيعية', 'فاطمة أحمد', '500', 'علوم', 'دار النهضة'),
                ('التربية الإسلامية', 'محمد عبدالله', '297', 'دين', 'دار الإسلام')
            ],
            'english': [
                ('Introduction to Computer Science', 'John Smith', '004', 'Computer Science', 'Tech Publications'),
                ('Modern Physics', 'Dr. Sarah Johnson', '530', 'Physics', 'Science Press'),
                ('World History', 'Michael Brown', '909', 'History', 'Academic Books'),
                ('English Literature', 'Emma Wilson', '820', 'Literature', 'Literary Press'),
                ('Mathematics for Beginners', 'David Lee', '510', 'Mathematics', 'Math Books Inc')
            ]
        }

        # Generate books
        for i in range(count):
            if language == 'all':
                lang_key = ['kurdish', 'arabic', 'english'][i % 3]
            else:
                lang_key = language

            samples = book_samples.get(lang_key, book_samples['english'])
            sample = samples[i % len(samples)]

            book = {
                'barcode': f"BOOK{i+1:06d}",
                'title': f"{sample[0]} - {i+1}",
                'author': sample[1],
                'ddc_number': sample[2],
                'author_code': sample[1][:3].upper(),
                'call_number': f"{sample[2]} {sample[1][:3].upper()}",
                'isbn': f"978-{i+1000000000:010d}",
                'category': sample[3],
                'publisher': sample[4],
                'publication_year': 2020 + (i % 4),
                'pages': 150 + (i % 300),
                'language': LANGUAGES[lang_key]['name'],
                'summary': f"This is a sample book about {sample[3].lower()}.",
                'location': f"Shelf-{chr(65 + i % 26)}-{i % 10 + 1}",
                'price': 10000 + (i % 50000),
                'condition_status': ['Good', 'Excellent', 'Fair'][i % 3],
                'notes': f"Sample book #{i+1}"
            }

            sample_books.append(book)

        return sample_books

    def generate_sample_members(self, count=30, language='all'):
        """Generate sample members data"""
        sample_members = []

        # Sample names for different languages
        name_samples = {
            'kurdish': [
                ('ئارام ئەحمەد عەلی', 'پۆلی نەهەم - A'),
                ('سارا محەمەد حەسەن', 'پۆلی هەشتەم - B'),
                ('ڕەوان ئیبراهیم ئەحمەد', 'پۆلی حەوتەم - A'),
                ('هیوا عوسمان کەریم', 'پۆلی شەشەم - C'),
                ('دلنیا سەلیم ڕەشید', 'پۆلی پێنجەم - B')
            ],
            'arabic': [
                ('أحمد محمد علي', 'الصف التاسع - أ'),
                ('فاطمة حسن محمود', 'الصف الثامن - ب'),
                ('عمر إبراهيم أحمد', 'الصف السابع - أ'),
                ('زينب عثمان كريم', 'الصف السادس - ج'),
                ('يوسف سليم رشيد', 'الصف الخامس - ب')
            ],
            'english': [
                ('John Michael Smith', 'Grade 9 - A'),
                ('Sarah Emily Johnson', 'Grade 8 - B'),
                ('David Robert Brown', 'Grade 7 - A'),
                ('Emma Grace Wilson', 'Grade 6 - C'),
                ('James Alexander Lee', 'Grade 5 - B')
            ]
        }

        # Generate members
        for i in range(count):
            if language == 'all':
                lang_key = ['kurdish', 'arabic', 'english'][i % 3]
            else:
                lang_key = language

            samples = name_samples.get(lang_key, name_samples['english'])
            sample = samples[i % len(samples)]

            member = {
                'member_id': f"{datetime.now().year}{i+1:04d}",
                'name': sample[0],
                'class_grade': sample[1],
                'national_id': f"{i+1000000000:010d}",
                'phone': f"075{i+1000000:07d}",
                'guardian_name': f"Guardian of {sample[0].split()[0]}",
                'guardian_phone': f"075{i+2000000:07d}",
                'address': f"Address {i+1}, City",
                'email': f"student{i+1}@school.edu",
                'date_of_birth': f"{2005 + i % 8}-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
                'gender': ['Male', 'Female'][i % 2],
                'expiry_date': f"{datetime.now().year + 1}-12-31",
                'membership_type': 'Student',
                'emergency_contact': f"Emergency Contact {i+1}",
                'emergency_phone': f"075{i+3000000:07d}",
                'notes': f"Sample member #{i+1}"
            }

            sample_members.append(member)

        return sample_members

    def export_sample_csv(self, data_type, language='all', count=50):
        """Export sample data to CSV"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            exports_folder = self.config.get('folders.exports', 'exports')

            if data_type == 'books':
                data = self.generate_sample_books(count, language)
                filename = f"sample_books_{language}_{timestamp}.csv"
            else:
                data = self.generate_sample_members(count, language)
                filename = f"sample_members_{language}_{timestamp}.csv"

            file_path = os.path.join(exports_folder, filename)

            if data:
                df = pd.DataFrame(data)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')

            return file_path

        except Exception as e:
            raise Exception(f"Error exporting sample data: {str(e)}")

# ==============================================================================
# Enhanced Modern Application
# ==============================================================================

class EnhancedModernLibraryApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        # Initialize configuration and managers
        self.config = ConfigManager()
        self.current_lang = self.config.get('language', 'kurdish')
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])

        # Initialize database and other managers
        self.db = EnhancedDatabaseManager(self.config)
        self.backup_manager = BackupRestoreManager(self.db, self.config)
        self.id_generator = EnhancedIDCardGenerator(self.db, self.config)
        self.sample_generator = SampleDataGenerator(self.config)

        # Configure window
        self.title(self.t.get('app_title', 'Library Management System'))
        self.geometry("1500x900")
        self.minsize(1200, 700)

        # Set appearance based on config
        appearance_mode = self.config.get('appearance_mode', 'light')
        ctk.set_appearance_mode(appearance_mode)

        # Create UI
        self.create_widgets()

        # Load initial data
        self.refresh_data()

        # Auto-save config on close
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    # Define all methods first (before UI creation)
    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = AddBookDialog(self, self.config, self.db)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_books_display()
            self.refresh_data()

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = AddMemberDialog(self, self.config, self.db)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_members_display()
            self.refresh_data()

    def generate_member_cards(self):
        """Generate ID cards for all active members"""
        try:
            # Get all active members
            query = "SELECT member_id FROM members WHERE status = 'Active'"
            active_members = self.db.execute_query(query, fetch=True)

            if not active_members:
                messagebox.showwarning(
                    self.t.get('warning', 'Warning'),
                    self.t.get('no_active_members', 'No active members found')
                )
                return

            # Generate cards for all members
            card_paths = []
            for member_row in active_members:
                member_id = member_row[0]
                try:
                    card_path = self.id_generator.generate_member_id_card(member_id)
                    card_paths.append(card_path)
                except Exception as e:
                    print(f"Error generating card for {member_id}: {e}")

            if card_paths:
                # Create beautiful HTML page
                html_path = self.id_generator.create_beautiful_html_cards(card_paths)

                # Open in browser
                webbrowser.open(f'file://{os.path.abspath(html_path)}')

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('id_cards_generated', 'ID cards generated successfully')}\n"
                    f"{self.t.get('total_cards', 'Total cards')}: {len(card_paths)}\n"
                    f"{self.t.get('html_file', 'HTML file')}: {html_path}"
                )
            else:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('no_cards_generated', 'No cards could be generated')
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('cards_generation_error', 'Error generating cards')}: {str(e)}"
            )

    def backup_data(self):
        """Create system backup"""
        try:
            backup_path, info = self.backup_manager.create_full_backup()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('backup_created_successfully', 'Backup created successfully')}\n{backup_path}"
            )

            # Show backup info in report display if available
            if hasattr(self, 'report_display'):
                report = f"""
{self.t.get('system_backup_report', 'SYSTEM BACKUP REPORT')}
{'=' * 50}

{self.t.get('backup_completed', 'Backup completed successfully!')}
{self.t.get('backup_location', 'Backup Location')}: {backup_path}

{self.t.get('backup_information', 'BACKUP INFORMATION')}:
{'-' * 30}
{self.t.get('backup_date', 'Backup Date')}: {info.get('backup_date', 'N/A')}
{self.t.get('system_version', 'System Version')}: {info.get('system_version', 'N/A')}
{self.t.get('database_version', 'Database Version')}: {info.get('database_version', 'N/A')}
{self.t.get('language', 'Language')}: {info.get('language', 'N/A')}
{self.t.get('school_name', 'School')}: {info.get('school_name', 'N/A')}

{self.t.get('backup_contents', 'BACKUP CONTENTS')}:
• {self.t.get('database_file', 'Complete database file')}
• {self.t.get('configuration_files', 'Configuration files')}
• {self.t.get('assets_and_photos', 'Assets and photos')}
• {self.t.get('member_barcodes', 'Member barcodes')}
• {self.t.get('backup_metadata', 'Backup metadata')}

{self.t.get('statistics', 'STATISTICS')}:
{self.t.get('total_books', 'Total Books')}: {info.get('total_books', 0):,}
{self.t.get('total_members', 'Total Members')}: {info.get('total_members', 0):,}
{self.t.get('backup_size', 'Backup Size')}: {os.path.getsize(backup_path) / 1024 / 1024:.1f} MB
"""

                self.report_display.delete("0.0", "end")
                self.report_display.insert("0.0", report)
                self.current_report_data = report

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('backup_error', 'Backup error')}: {str(e)}"
            )

    # Define all report methods first (before UI creation)
    def generate_statistics_report(self):
        """Generate comprehensive statistics report"""
        try:
            self.current_report_type = 'statistics'

            # Get data
            books = self.db.get_all_books()
            members = self.db.get_all_members()

            # Calculate statistics
            total_books = len(books)
            available_books = len([b for b in books if b[14] == 'Available'])
            borrowed_books = total_books - available_books

            total_members = len(members)
            active_members = len([m for m in members if m[15] == 'Active'])

            # Get transaction statistics
            transactions_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow' AND transaction_date >= date('now', '-30 days')
            '''
            monthly_borrows = self.db.execute_query(transactions_query, fetch=True)[0][0]

            overdue_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow' AND return_date IS NULL AND due_date < date('now')
            '''
            overdue_count = self.db.execute_query(overdue_query, fetch=True)[0][0]

            # Generate report
            report = f"""
{self.t.get('library_statistics_report', 'LIBRARY STATISTICS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('school_name', 'School')}: {self.config.get('school_info.name', 'N/A')}

{self.t.get('book_statistics', 'BOOK STATISTICS')}:
{'-' * 30}
{self.t.get('total_books', 'Total Books')}: {total_books:,}
{self.t.get('available_books', 'Available Books')}: {available_books:,}
{self.t.get('borrowed_books', 'Borrowed Books')}: {borrowed_books:,}
{self.t.get('availability_rate', 'Availability Rate')}: {(available_books/total_books*100) if total_books > 0 else 0:.1f}%

{self.t.get('member_statistics', 'MEMBER STATISTICS')}:
{'-' * 30}
{self.t.get('total_members', 'Total Members')}: {total_members:,}
{self.t.get('active_members', 'Active Members')}: {active_members:,}
{self.t.get('inactive_members', 'Inactive Members')}: {total_members - active_members:,}
{self.t.get('activity_rate', 'Activity Rate')}: {(active_members/total_members*100) if total_members > 0 else 0:.1f}%

{self.t.get('circulation_statistics', 'CIRCULATION STATISTICS')}:
{'-' * 30}
{self.t.get('monthly_borrows', 'Borrows This Month')}: {monthly_borrows:,}
{self.t.get('overdue_books', 'Overdue Books')}: {overdue_count:,}
{self.t.get('overdue_rate', 'Overdue Rate')}: {(overdue_count/borrowed_books*100) if borrowed_books > 0 else 0:.1f}%

{self.t.get('system_information', 'SYSTEM INFORMATION')}:
{'-' * 30}
{self.t.get('database_file', 'Database File')}: {self.db.db_file}
{self.t.get('current_language', 'Current Language')}: {LANGUAGES[self.current_lang]['name']}
{self.t.get('loan_period', 'Loan Period')}: {self.config.get('library_settings.loan_period_days', 14)} {self.t.get('days', 'days')}
{self.t.get('max_books_per_student', 'Max Books per Student')}: {self.config.get('library_settings.max_books_per_student', 3)}
"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_overdue_report(self):
        """Generate overdue books report"""
        try:
            self.current_report_type = 'overdue'

            # Get overdue books
            overdue_query = '''
                SELECT b.title, b.author, b.barcode, m.name, m.member_id, m.phone,
                       t.transaction_date, t.due_date,
                       julianday('now') - julianday(t.due_date) as days_overdue,
                       t.fine_amount
                FROM transactions t
                JOIN books b ON t.book_barcode = b.barcode
                JOIN members m ON t.member_id = m.member_id
                WHERE t.transaction_type = 'borrow'
                  AND t.return_date IS NULL
                  AND t.due_date < date('now')
                ORDER BY days_overdue DESC
            '''

            overdue_books = self.db.execute_query(overdue_query, fetch=True)

            report = f"""
{self.t.get('overdue_books_report', 'OVERDUE BOOKS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('total_overdue', 'Total Overdue Books')}: {len(overdue_books)}

"""

            if overdue_books:
                fine_per_day = self.config.get('library_settings.fine_per_day', 250)
                currency = self.config.get('library_settings.currency', 'IQD')
                total_fines = 0

                for i, book in enumerate(overdue_books, 1):
                    days_overdue = int(book[8])
                    calculated_fine = days_overdue * fine_per_day
                    total_fines += calculated_fine

                    report += f"""
{i}. {self.t.get('book', 'Book')}: {book[0]}
   {self.t.get('author', 'Author')}: {book[1]}
   {self.t.get('barcode', 'Barcode')}: {book[2]}
   {self.t.get('borrower', 'Borrower')}: {book[3]} (ID: {book[4]})
   {self.t.get('phone', 'Phone')}: {book[5] or 'N/A'}
   {self.t.get('borrowed_date', 'Borrowed')}: {book[6]}
   {self.t.get('due_date', 'Due Date')}: {book[7]}
   {self.t.get('days_overdue', 'Days Overdue')}: {days_overdue}
   {self.t.get('fine_amount', 'Fine Amount')}: {calculated_fine:,} {currency}
   {'-' * 40}
"""

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('total_overdue_books', 'Total Overdue Books')}: {len(overdue_books)}
{self.t.get('total_fines', 'Total Fines')}: {total_fines:,} {currency}
{self.t.get('average_days_overdue', 'Average Days Overdue')}: {sum(int(b[8]) for b in overdue_books) / len(overdue_books):.1f}
"""
            else:
                report += f"\n🎉 {self.t.get('no_overdue_books', 'No overdue books found!')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_active_members_report(self):
        """Generate active members report"""
        try:
            self.current_report_type = 'active_members'

            # Get active members with current loans
            active_query = '''
                SELECT DISTINCT m.name, m.member_id, m.class_grade, m.phone,
                       COUNT(t.id) as current_loans,
                       GROUP_CONCAT(b.title, '; ') as borrowed_books
                FROM members m
                LEFT JOIN transactions t ON m.member_id = t.member_id
                    AND t.transaction_type = 'borrow'
                    AND t.return_date IS NULL
                LEFT JOIN books b ON t.book_barcode = b.barcode
                WHERE m.status = 'Active'
                GROUP BY m.id, m.name, m.member_id, m.class_grade, m.phone
                ORDER BY current_loans DESC, m.name
            '''

            active_members = self.db.execute_query(active_query, fetch=True)

            report = f"""
{self.t.get('active_members_report', 'ACTIVE MEMBERS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('total_active_members', 'Total Active Members')}: {len(active_members)}

"""

            if active_members:
                members_with_loans = len([m for m in active_members if m[4] > 0])
                total_loans = sum(m[4] for m in active_members)

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('members_with_loans', 'Members with Current Loans')}: {members_with_loans}
{self.t.get('members_without_loans', 'Members without Loans')}: {len(active_members) - members_with_loans}
{self.t.get('total_active_loans', 'Total Active Loans')}: {total_loans}
{self.t.get('average_loans_per_member', 'Average Loans per Member')}: {total_loans / len(active_members):.1f}

{self.t.get('detailed_list', 'DETAILED LIST')}:
{'-' * 40}
"""

                for i, member in enumerate(active_members, 1):
                    status_icon = "📚" if member[4] > 0 else "👤"
                    report += f"""
{i}. {status_icon} {member[0]} (ID: {member[1]})
   {self.t.get('class_grade', 'Class')}: {member[2] or 'N/A'}
   {self.t.get('phone', 'Phone')}: {member[3] or 'N/A'}
   {self.t.get('current_loans', 'Current Loans')}: {member[4]}
"""
                    if member[5] and member[4] > 0:
                        report += f"   {self.t.get('borrowed_books', 'Books')}: {member[5]}\n"
                    report += f"   {'-' * 35}\n"

            else:
                report += f"\n{self.t.get('no_active_members', 'No active members found.')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_monthly_report(self):
        """Generate monthly activity report"""
        try:
            self.current_report_type = 'monthly'

            current_month = datetime.now().strftime('%Y-%m')

            # Get monthly statistics
            monthly_borrows_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow'
                  AND strftime('%Y-%m', transaction_date) = ?
            '''
            monthly_borrows = self.db.execute_query(monthly_borrows_query, (current_month,), fetch=True)[0][0]

            monthly_returns_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow'
                  AND return_date IS NOT NULL
                  AND strftime('%Y-%m', return_date) = ?
            '''
            monthly_returns = self.db.execute_query(monthly_returns_query, (current_month,), fetch=True)[0][0]

            new_members_query = '''
                SELECT COUNT(*) FROM members
                WHERE strftime('%Y-%m', registration_date) = ?
            '''
            new_members = self.db.execute_query(new_members_query, (current_month,), fetch=True)[0][0]

            new_books_query = '''
                SELECT COUNT(*) FROM books
                WHERE strftime('%Y-%m', date_added) = ?
            '''
            new_books = self.db.execute_query(new_books_query, (current_month,), fetch=True)[0][0]

            # Get most active members this month
            active_members_query = '''
                SELECT m.name, m.member_id, COUNT(t.id) as borrows_count
                FROM transactions t
                JOIN members m ON t.member_id = m.member_id
                WHERE t.transaction_type = 'borrow'
                  AND strftime('%Y-%m', t.transaction_date) = ?
                GROUP BY m.id, m.name, m.member_id
                ORDER BY borrows_count DESC
                LIMIT 5
            '''
            active_members = self.db.execute_query(active_members_query, (current_month,), fetch=True)

            report = f"""
{self.t.get('monthly_activity_report', 'MONTHLY ACTIVITY REPORT')}
{'=' * 50}

{self.t.get('report_period', 'Report Period')}: {datetime.now().strftime('%B %Y')}
{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{self.t.get('monthly_statistics', 'MONTHLY STATISTICS')}:
{'-' * 30}
{self.t.get('books_borrowed', 'Books Borrowed')}: {monthly_borrows:,}
{self.t.get('books_returned', 'Books Returned')}: {monthly_returns:,}
{self.t.get('new_members', 'New Members')}: {new_members:,}
{self.t.get('new_books', 'New Books Added')}: {new_books:,}

{self.t.get('circulation_activity', 'CIRCULATION ACTIVITY')}:
{'-' * 30}
{self.t.get('net_circulation', 'Net Circulation')}: {monthly_borrows - monthly_returns:,}
{self.t.get('return_rate', 'Return Rate')}: {(monthly_returns/monthly_borrows*100) if monthly_borrows > 0 else 0:.1f}%
"""

            if active_members:
                report += f"""
{self.t.get('most_active_members', 'MOST ACTIVE MEMBERS THIS MONTH')}:
{'-' * 40}
"""
                for i, member in enumerate(active_members, 1):
                    report += f"{i}. {member[0]} (ID: {member[1]}) - {member[2]} {self.t.get('borrows', 'borrows')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_popular_books_report(self):
        """Generate popular books report"""
        try:
            self.current_report_type = 'popular_books'

            # Get most borrowed books
            popular_query = '''
                SELECT b.title, b.author, b.category, b.barcode,
                       COUNT(t.id) as borrow_count,
                       MAX(t.transaction_date) as last_borrowed
                FROM transactions t
                JOIN books b ON t.book_barcode = b.barcode
                WHERE t.transaction_type = 'borrow'
                GROUP BY b.id, b.title, b.author, b.category, b.barcode
                ORDER BY borrow_count DESC, last_borrowed DESC
                LIMIT 20
            '''

            popular_books = self.db.execute_query(popular_query, fetch=True)

            report = f"""
{self.t.get('popular_books_report', 'POPULAR BOOKS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('top_books', 'Top 20 Most Borrowed Books')}

"""

            if popular_books:
                total_borrows = sum(book[4] for book in popular_books)

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('total_books_analyzed', 'Total Books Analyzed')}: {len(popular_books)}
{self.t.get('total_borrows', 'Total Borrows')}: {total_borrows:,}
{self.t.get('average_borrows', 'Average Borrows per Book')}: {total_borrows / len(popular_books):.1f}

{self.t.get('ranking', 'RANKING')}:
{'-' * 40}
"""

                for i, book in enumerate(popular_books, 1):
                    percentage = (book[4] / total_borrows * 100) if total_borrows > 0 else 0
                    report += f"""
{i:2d}. 📚 {book[0]}
    {self.t.get('author', 'Author')}: {book[1]}
    {self.t.get('category', 'Category')}: {book[2] or 'N/A'}
    {self.t.get('barcode', 'Barcode')}: {book[3]}
    {self.t.get('times_borrowed', 'Times Borrowed')}: {book[4]} ({percentage:.1f}%)
    {self.t.get('last_borrowed', 'Last Borrowed')}: {book[5]}
    {'-' * 35}
"""
            else:
                report += f"\n{self.t.get('no_borrowing_history', 'No borrowing history found.')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_fines_report(self):
        """Generate fines report"""
        try:
            self.current_report_type = 'fines'

            # Get outstanding fines
            fines_query = '''
                SELECT m.name, m.member_id, m.phone, b.title, b.barcode,
                       t.transaction_date, t.due_date, t.fine_amount,
                       julianday('now') - julianday(t.due_date) as days_overdue,
                       t.fine_paid
                FROM transactions t
                JOIN members m ON t.member_id = m.member_id
                JOIN books b ON t.book_barcode = b.barcode
                WHERE t.transaction_type = 'borrow'
                  AND (t.fine_amount > 0 OR t.due_date < date('now'))
                  AND (t.return_date IS NULL OR t.fine_paid = 0)
                ORDER BY t.fine_amount DESC, days_overdue DESC
            '''

            fines_data = self.db.execute_query(fines_query, fetch=True)

            report = f"""
{self.t.get('fines_report', 'FINES REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('fine_per_day', 'Fine per Day')}: {self.config.get('library_settings.fine_per_day', 250)} {self.config.get('library_settings.currency', 'IQD')}

"""

            if fines_data:
                currency = self.config.get('library_settings.currency', 'IQD')
                fine_per_day = self.config.get('library_settings.fine_per_day', 250)

                total_fines = 0
                paid_fines = 0
                unpaid_fines = 0

                for fine in fines_data:
                    if fine[7] > 0:  # existing fine amount
                        if fine[9]:  # fine_paid
                            paid_fines += fine[7]
                        else:
                            unpaid_fines += fine[7]
                        total_fines += fine[7]
                    else:  # calculate fine for overdue
                        days_overdue = max(0, int(fine[8]))
                        calculated_fine = days_overdue * fine_per_day
                        unpaid_fines += calculated_fine
                        total_fines += calculated_fine

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('total_fines', 'Total Fines')}: {total_fines:,} {currency}
{self.t.get('paid_fines', 'Paid Fines')}: {paid_fines:,} {currency}
{self.t.get('unpaid_fines', 'Unpaid Fines')}: {unpaid_fines:,} {currency}
{self.t.get('collection_rate', 'Collection Rate')}: {(paid_fines/total_fines*100) if total_fines > 0 else 0:.1f}%

{self.t.get('detailed_fines', 'DETAILED FINES LIST')}:
{'-' * 40}
"""

                for i, fine in enumerate(fines_data, 1):
                    days_overdue = max(0, int(fine[8]))
                    fine_amount = fine[7] if fine[7] > 0 else days_overdue * fine_per_day
                    status = "✅ PAID" if fine[9] else "❌ UNPAID"

                    report += f"""
{i}. {fine[0]} (ID: {fine[1]})
   {self.t.get('phone', 'Phone')}: {fine[2] or 'N/A'}
   {self.t.get('book', 'Book')}: {fine[3]}
   {self.t.get('barcode', 'Barcode')}: {fine[4]}
   {self.t.get('borrowed_date', 'Borrowed')}: {fine[5]}
   {self.t.get('due_date', 'Due Date')}: {fine[6]}
   {self.t.get('days_overdue', 'Days Overdue')}: {days_overdue}
   {self.t.get('fine_amount', 'Fine Amount')}: {fine_amount:,} {currency}
   {self.t.get('status', 'Status')}: {status}
   {'-' * 35}
"""
            else:
                report += f"\n🎉 {self.t.get('no_outstanding_fines', 'No outstanding fines found!')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def export_complete_data(self):
        """Export complete system data"""
        try:
            export_path, info = self.backup_manager.export_data_json()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('data_exported_successfully', 'Data exported successfully')}\n{export_path}"
            )

            # Show export info in report display
            report = f"""
{self.t.get('complete_data_export', 'COMPLETE DATA EXPORT')}
{'=' * 50}

{self.t.get('export_completed', 'Export completed successfully!')}
{self.t.get('file_location', 'File Location')}: {export_path}

{self.t.get('export_information', 'EXPORT INFORMATION')}:
{'-' * 30}
{self.t.get('export_date', 'Export Date')}: {info.get('export_date', 'N/A')}
{self.t.get('system_version', 'System Version')}: {info.get('system_version', 'N/A')}
{self.t.get('language', 'Language')}: {info.get('language', 'N/A')}
{self.t.get('school_name', 'School')}: {info.get('school_info', {}).get('name', 'N/A')}

{self.t.get('data_included', 'DATA INCLUDED')}:
• {self.t.get('all_books', 'All books with complete information')}
• {self.t.get('all_members', 'All members with complete profiles')}
• {self.t.get('all_transactions', 'All transaction history')}
• {self.t.get('system_settings', 'System settings and configuration')}

{self.t.get('file_format', 'File Format')}: JSON
{self.t.get('encoding', 'Encoding')}: UTF-8
{self.t.get('file_size', 'File Size')}: {os.path.getsize(export_path) / 1024:.1f} KB
"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('export_error', 'Export error')}: {str(e)}"
            )

    def backup_data(self):
        """Create system backup"""
        try:
            backup_path, info = self.backup_manager.create_full_backup()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('backup_created_successfully', 'Backup created successfully')}\n{backup_path}"
            )

            # Show backup info in report display
            report = f"""
{self.t.get('system_backup_report', 'SYSTEM BACKUP REPORT')}
{'=' * 50}

{self.t.get('backup_completed', 'Backup completed successfully!')}
{self.t.get('backup_location', 'Backup Location')}: {backup_path}

{self.t.get('backup_information', 'BACKUP INFORMATION')}:
{'-' * 30}
{self.t.get('backup_date', 'Backup Date')}: {info.get('backup_date', 'N/A')}
{self.t.get('system_version', 'System Version')}: {info.get('system_version', 'N/A')}
{self.t.get('database_version', 'Database Version')}: {info.get('database_version', 'N/A')}
{self.t.get('language', 'Language')}: {info.get('language', 'N/A')}
{self.t.get('school_name', 'School')}: {info.get('school_name', 'N/A')}

{self.t.get('backup_contents', 'BACKUP CONTENTS')}:
• {self.t.get('database_file', 'Complete database file')}
• {self.t.get('configuration_files', 'Configuration files')}
• {self.t.get('assets_and_photos', 'Assets and photos')}
• {self.t.get('member_barcodes', 'Member barcodes')}
• {self.t.get('backup_metadata', 'Backup metadata')}

{self.t.get('statistics', 'STATISTICS')}:
{self.t.get('total_books', 'Total Books')}: {info.get('total_books', 0):,}
{self.t.get('total_members', 'Total Members')}: {info.get('total_members', 0):,}
{self.t.get('backup_size', 'Backup Size')}: {os.path.getsize(backup_path) / 1024 / 1024:.1f} MB

{self.t.get('restore_instructions', 'RESTORE INSTRUCTIONS')}:
1. {self.t.get('go_to_settings', 'Go to Settings page')}
2. {self.t.get('click_import_settings', 'Click "Import Settings"')}
3. {self.t.get('select_backup_file', 'Select this backup file')}
4. {self.t.get('confirm_restore', 'Confirm restore operation')}
"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('backup_error', 'Backup error')}: {str(e)}"
            )

    def save_current_report(self):
        """Save current report to file"""
        if not hasattr(self, 'current_report_data') or not self.current_report_data:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('no_report_to_save', 'No report to save')
            )
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_type = getattr(self, 'current_report_type', 'report')

            file_path = filedialog.asksaveasfilename(
                title=self.t.get('save_report', 'Save Report'),
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ],
                initialname=f"{report_type}_report_{timestamp}.txt"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_report_data)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('report_saved_successfully', 'Report saved successfully')}\n{file_path}"
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_error', 'Error saving report')}: {str(e)}"
            )

    def print_current_report(self):
        """Print current report"""
        if not hasattr(self, 'current_report_data') or not self.current_report_data:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('no_report_to_print', 'No report to print')
            )
            return

        try:
            # Create temporary HTML file for printing
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file = os.path.join(tempfile.gettempdir(), f"library_report_{timestamp}.html")

            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Library Report</title>
    <style>
        body {{ font-family: 'Courier New', monospace; margin: 20px; }}
        pre {{ white-space: pre-wrap; word-wrap: break-word; }}
        @media print {{
            body {{ margin: 0; }}
        }}
    </style>
</head>
<body>
    <pre>{self.current_report_data}</pre>
</body>
</html>
"""

            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # Open in browser for printing
            webbrowser.open(f'file://{os.path.abspath(temp_file)}')

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('report_opened_for_printing', 'Report opened in browser for printing')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('print_error', 'Error preparing report for printing')}: {str(e)}"
            )

    def refresh_current_report(self):
        """Refresh current report"""
        if hasattr(self, 'current_report_type') and self.current_report_type:
            report_methods = {
                'statistics': self.generate_statistics_report,
                'overdue': self.generate_overdue_report,
                'active_members': self.generate_active_members_report,
                'monthly': self.generate_monthly_report,
                'popular_books': self.generate_popular_books_report,
                'fines': self.generate_fines_report
            }

            if self.current_report_type in report_methods:
                report_methods[self.current_report_type]()
            else:
                messagebox.showinfo(
                    self.t.get('info', 'Info'),
                    self.t.get('select_report_first', 'Please select a report type first')
                )
        else:
            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('select_report_first', 'Please select a report type first')
            )

    # Settings functionality
    def update_system_info(self):
        """Update system information display"""
        try:
            # Clear existing info
            for widget in self.system_info_display.winfo_children():
                widget.destroy()

            # Get system information
            books_count = len(self.db.get_all_books())
            members_count = len(self.db.get_all_members())
            db_size = os.path.getsize(self.db.db_file) / 1024  # KB

            # System info items
            info_items = [
                (f"📊 {self.t.get('total_books', 'Total Books')}", f"{books_count:,}"),
                (f"👥 {self.t.get('total_members', 'Total Members')}", f"{members_count:,}"),
                (f"💾 {self.t.get('database_size', 'Database Size')}", f"{db_size:.1f} KB"),
                (f"🗂️ {self.t.get('database_file', 'Database File')}", self.db.db_file),
                (f"🌐 {self.t.get('current_language', 'Current Language')}", LANGUAGES[self.current_lang]['name']),
                (f"🎨 {self.t.get('appearance_mode', 'Appearance Mode')}", self.config.get('appearance_mode', 'light')),
                (f"📅 {self.t.get('system_date', 'System Date')}", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            ]

            for label, value in info_items:
                item_frame = ctk.CTkFrame(self.system_info_display)
                item_frame.pack(fill="x", padx=10, pady=2)

                ctk.CTkLabel(item_frame, text=label, font=ctk.CTkFont(size=11, weight="bold")).pack(anchor="w", padx=10, pady=(5, 2))
                ctk.CTkLabel(item_frame, text=value, font=ctk.CTkFont(size=10), wraplength=250).pack(anchor="w", padx=10, pady=(0, 5))

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.system_info_display,
                text=f"{self.t.get('error_loading_system_info', 'Error loading system info')}: {str(e)}",
                font=ctk.CTkFont(size=10)
            )
            error_label.pack(padx=10, pady=10)

    def change_appearance_mode(self, mode):
        """Change appearance mode"""
        try:
            ctk.set_appearance_mode(mode)
            self.config.set('appearance_mode', mode)

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('appearance_changed', 'Appearance mode changed successfully')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('appearance_error', 'Error changing appearance')}: {str(e)}"
            )

    def change_color_theme(self, theme):
        """Change color theme"""
        try:
            ctk.set_default_color_theme(theme)
            self.config.set('color_theme', theme)

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                f"{self.t.get('theme_changed', 'Color theme changed')}. {self.t.get('restart_required', 'Please restart the application to see changes.')}"
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('theme_error', 'Error changing theme')}: {str(e)}"
            )

    def save_settings(self):
        """Save all settings"""
        try:
            # Save school information
            for field_key, entry in self.school_entries.items():
                self.config.set(f'school_info.{field_key}', entry.get().strip())

            # Save library settings
            for field_key, entry in self.library_entries.items():
                value = entry.get().strip()
                if field_key in ['loan_period_days', 'max_books_per_student', 'fine_per_day']:
                    try:
                        value = int(value) if value.isdigit() else self.config.get(f'library_settings.{field_key}')
                    except:
                        value = self.config.get(f'library_settings.{field_key}')

                self.config.set(f'library_settings.{field_key}', value)

            # Save configuration
            self.config.save_config()

            # Update system info
            self.update_system_info()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('settings_saved_successfully', 'Settings saved successfully')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_settings_error', 'Error saving settings')}: {str(e)}"
            )

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno(
            self.t.get('confirm', 'Confirm'),
            self.t.get('reset_settings_confirm', 'Are you sure you want to reset all settings to defaults?')
        ):
            try:
                # Reset to default configuration
                self.config.config = DEFAULT_CONFIG.copy()
                self.config.save_config()

                # Update UI with default values
                for field_key, entry in self.school_entries.items():
                    entry.delete(0, "end")
                    entry.insert(0, DEFAULT_CONFIG['school_info'].get(field_key, ''))

                for field_key, entry in self.library_entries.items():
                    entry.delete(0, "end")
                    entry.insert(0, str(DEFAULT_CONFIG['library_settings'].get(field_key, '')))

                # Reset appearance settings
                self.appearance_mode_var.set(DEFAULT_CONFIG['appearance_mode'])
                self.color_theme_var.set(DEFAULT_CONFIG['color_theme'])

                # Update system info
                self.update_system_info()

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    self.t.get('settings_reset_successfully', 'Settings reset to defaults successfully')
                )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('reset_error', 'Error resetting settings')}: {str(e)}"
                )

    def import_settings(self):
        """Import settings from backup file"""
        file_path = filedialog.askopenfilename(
            title=self.t.get('select_backup_file', 'Select Backup File'),
            filetypes=[
                ("Backup files", "*.zip"),
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.zip'):
                    # Restore from backup
                    success, info = self.backup_manager.restore_from_backup(file_path)
                    if success:
                        messagebox.showinfo(
                            self.t.get('success', 'Success'),
                            f"{self.t.get('backup_restored_successfully', 'Backup restored successfully')}\n"
                            f"{self.t.get('restart_required', 'Please restart the application.')}"
                        )
                elif file_path.endswith('.json'):
                    # Import JSON configuration
                    with open(file_path, 'r', encoding='utf-8') as f:
                        imported_config = json.load(f)

                    # Merge with current config
                    self.config.config = self.config.merge_config(self.config.config, imported_config)
                    self.config.save_config()

                    messagebox.showinfo(
                        self.t.get('success', 'Success'),
                        f"{self.t.get('settings_imported_successfully', 'Settings imported successfully')}\n"
                        f"{self.t.get('restart_required', 'Please restart the application.')}"
                    )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('import_error', 'Error importing settings')}: {str(e)}"
                )

    def export_settings(self):
        """Export current settings"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            file_path = filedialog.asksaveasfilename(
                title=self.t.get('export_settings', 'Export Settings'),
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ],
                initialname=f"library_settings_{timestamp}.json"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config.config, f, ensure_ascii=False, indent=4)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('settings_exported_successfully', 'Settings exported successfully')}\n{file_path}"
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('export_error', 'Error exporting settings')}: {str(e)}"
            )
    
    def create_widgets(self):
        """Create enhanced UI widgets"""
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Create sidebar
        self.create_enhanced_sidebar()

        # Create main content area
        self.create_enhanced_main_content()

    def on_closing(self):
        """Handle application closing"""
        try:
            # Save any pending changes
            self.config.save_config()

            # Log application close
            self.db.log_activity('CLOSE', 'APPLICATION', 'system', 'Application closed')

            self.destroy()
        except Exception as e:
            print(f"Error during closing: {e}")
            self.destroy()
    
    def create_enhanced_sidebar(self):
        """Create enhanced modern sidebar"""
        self.sidebar_frame = ctk.CTkFrame(self, width=320, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(8, weight=1)

        # School logo and title
        school_name = self.config.get('school_info.name', 'Library System')

        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame,
            text=school_name,
            font=ctk.CTkFont(size=18, weight="bold"),
            wraplength=280
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 5))

        self.subtitle_label = ctk.CTkLabel(
            self.sidebar_frame,
            text=self.t.get('app_title', 'Library Management System'),
            font=ctk.CTkFont(size=12),
            wraplength=280
        )
        self.subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 20))
        
        # Language selector
        lang_frame = ctk.CTkFrame(self.sidebar_frame)
        lang_frame.grid(row=2, column=0, padx=20, pady=(0, 10), sticky="ew")

        ctk.CTkLabel(lang_frame, text="🌐", font=ctk.CTkFont(size=16)).pack(side="left", padx=(10, 5))

        self.language_var = ctk.StringVar(value=self.current_lang)
        self.language_menu = ctk.CTkOptionMenu(
            lang_frame,
            variable=self.language_var,
            values=list(LANGUAGES.keys()),
            command=self.change_language,
            width=120
        )
        self.language_menu.pack(side="left", padx=(0, 10), pady=5)

        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("dashboard", self.t.get('dashboard', 'Dashboard'), "🏠"),
            ("circulation", self.t.get('circulation', 'Circulation'), "🔄"),
            ("books", self.t.get('books', 'Books'), "📚"),
            ("members", self.t.get('members', 'Members'), "👥"),
            ("reports", self.t.get('reports', 'Reports'), "📊"),
            ("settings", self.t.get('settings', 'Settings'), "⚙️")
        ]

        for i, (key, text, icon) in enumerate(nav_items):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=f"{icon} {text}",
                command=lambda k=key: self.show_page(k),
                height=45,
                font=ctk.CTkFont(size=13),
                anchor="w"
            )
            btn.grid(row=i+3, column=0, padx=20, pady=3, sticky="ew")
            self.nav_buttons[key] = btn
        
        # Quick stats with enhanced design
        self.stats_frame = ctk.CTkFrame(self.sidebar_frame)
        self.stats_frame.grid(row=9, column=0, padx=20, pady=20, sticky="ew")

        self.stats_title = ctk.CTkLabel(
            self.stats_frame,
            text=f"📊 {self.t.get('quick_stats', 'Quick Stats')}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.stats_title.pack(pady=(15, 10))

        # Stats display with better formatting
        self.stats_display = ctk.CTkFrame(self.stats_frame)
        self.stats_display.pack(padx=10, pady=(0, 15), fill="x")

        # Individual stat items
        self.stat_items = {}
        stat_keys = ['total_books', 'available_books', 'borrowed_books', 'overdue_books', 'total_members']

        for key in stat_keys:
            item_frame = ctk.CTkFrame(self.stats_display)
            item_frame.pack(fill="x", padx=5, pady=2)

            label = ctk.CTkLabel(item_frame, text=f"{self.t.get(key, key.replace('_', ' ').title())}:",
                               font=ctk.CTkFont(size=10))
            label.pack(side="left", padx=(8, 5), pady=5)

            value = ctk.CTkLabel(item_frame, text="0", font=ctk.CTkFont(size=10, weight="bold"))
            value.pack(side="right", padx=(5, 8), pady=5)

            self.stat_items[key] = value

        # System info
        info_frame = ctk.CTkFrame(self.sidebar_frame)
        info_frame.grid(row=10, column=0, padx=20, pady=(0, 20), sticky="ew")

        version_label = ctk.CTkLabel(info_frame, text="📱 Version 7.0", font=ctk.CTkFont(size=10))
        version_label.pack(pady=(10, 5))

        time_label = ctk.CTkLabel(info_frame, text="", font=ctk.CTkFont(size=9))
        time_label.pack(pady=(0, 10))
        self.time_label = time_label

        # Update time every second
        self.update_time()
    
    def create_enhanced_main_content(self):
        """Create enhanced main content area"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # Create pages
        self.pages = {}
        self.create_all_enhanced_pages()

        # Show dashboard by default
        self.show_page("dashboard")

    def update_time(self):
        """Update time display"""
        try:
            current_time = datetime.now().strftime('%H:%M:%S')
            current_date = datetime.now().strftime('%Y-%m-%d')
            self.time_label.configure(text=f"{current_date}\n{current_time}")

            # Schedule next update
            self.after(1000, self.update_time)
        except:
            pass

    def change_language(self, new_language):
        """Change application language"""
        try:
            if new_language != self.current_lang:
                # Save new language to config
                self.config.set('language', new_language)

                # Show restart message
                restart_msg = {
                    'kurdish': 'زمان گۆڕا. تکایە بەرنامەکە دووبارە دەستپێبکەرەوە بۆ جێبەجێکردنی گۆڕانکارییەکان.',
                    'arabic': 'تم تغيير اللغة. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.',
                    'english': 'Language changed. Please restart the application to apply changes.'
                }

                messagebox.showinfo("Language Changed", restart_msg.get(new_language, restart_msg['english']))
        except Exception as e:
            print(f"Error changing language: {e}")
    
    def create_all_enhanced_pages(self):
        """Create all enhanced application pages"""
        # Enhanced Dashboard page
        self.create_enhanced_dashboard_page()

        # Enhanced Circulation page
        self.create_enhanced_circulation_page()

        # Enhanced Books page
        self.create_enhanced_books_page()

        # Enhanced Members page
        self.create_enhanced_members_page()

        # Enhanced Reports page
        self.create_enhanced_reports_page()

        # Enhanced Settings page
        self.create_enhanced_settings_page()

    def create_enhanced_dashboard_page(self):
        """Create enhanced dashboard page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["dashboard"] = page

        # Welcome header
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        # Welcome message with school info
        school_name = self.config.get('school_info.name', 'Library System')
        welcome_text = f"🏫 {self.t.get('welcome_to', 'Welcome to')} {school_name}"

        ctk.CTkLabel(
            header_frame,
            text=welcome_text,
            font=ctk.CTkFont(size=22, weight="bold"),
            wraplength=800
        ).pack(side="left", padx=20, pady=15)

        # Current date and time
        current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M')
        ctk.CTkLabel(
            header_frame,
            text=f"📅 {current_datetime}",
            font=ctk.CTkFont(size=12)
        ).pack(side="right", padx=20, pady=15)

        # Enhanced stats cards with better design
        stats_container = ctk.CTkFrame(page)
        stats_container.pack(fill="x", padx=20, pady=(0, 15))

        # Stats grid
        stats_grid = ctk.CTkFrame(stats_container)
        stats_grid.pack(fill="x", padx=20, pady=20)

        # Configure grid
        for i in range(4):
            stats_grid.grid_columnconfigure(i, weight=1)

        # Create enhanced stat cards
        self.stat_cards = {}
        stat_items = [
            ("total_books", self.t.get('total_books', 'Total Books'), "📚", COLORS['primary']),
            ("available_books", self.t.get('available_books', 'Available'), "✅", COLORS['success']),
            ("borrowed_books", self.t.get('borrowed_books', 'Borrowed'), "📖", COLORS['warning']),
            ("overdue_books", self.t.get('overdue_books', 'Overdue'), "⚠️", COLORS['danger'])
        ]

        for i, (key, title, icon, color) in enumerate(stat_items):
            card = ctk.CTkFrame(stats_grid, fg_color=color, corner_radius=15)
            card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")

            # Icon
            icon_label = ctk.CTkLabel(card, text=icon, font=ctk.CTkFont(size=32))
            icon_label.pack(pady=(20, 5))

            # Value
            value_label = ctk.CTkLabel(card, text="0", font=ctk.CTkFont(size=28, weight="bold"),
                                     text_color="white")
            value_label.pack(pady=(0, 5))
            self.stat_cards[key] = value_label

            # Title
            title_label = ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=12, weight="bold"),
                                     text_color="white", wraplength=120)
            title_label.pack(pady=(0, 20))

        # Additional stats row
        stats_grid2 = ctk.CTkFrame(stats_container)
        stats_grid2.pack(fill="x", padx=20, pady=(0, 20))

        for i in range(3):
            stats_grid2.grid_columnconfigure(i, weight=1)

        additional_stats = [
            ("total_members", self.t.get('total_members', 'Total Members'), "👥", COLORS['secondary']),
            ("active_members", self.t.get('active_members', 'Active Members'), "🟢", COLORS['success']),
            ("new_this_month", self.t.get('new_this_month', 'New This Month'), "📈", COLORS['accent'])
        ]

        for i, (key, title, icon, color) in enumerate(additional_stats):
            card = ctk.CTkFrame(stats_grid2, fg_color=color, corner_radius=15)
            card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")

            icon_label = ctk.CTkLabel(card, text=icon, font=ctk.CTkFont(size=24))
            icon_label.pack(pady=(15, 5))

            value_label = ctk.CTkLabel(card, text="0", font=ctk.CTkFont(size=20, weight="bold"),
                                     text_color="white")
            value_label.pack(pady=(0, 5))
            self.stat_cards[key] = value_label

            title_label = ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=11, weight="bold"),
                                     text_color="white", wraplength=100)
            title_label.pack(pady=(0, 15))

        # Quick actions section
        actions_frame = ctk.CTkFrame(page)
        actions_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(
            actions_frame,
            text=f"⚡ {self.t.get('quick_actions', 'Quick Actions')}",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=20, pady=(15, 10))

        # Action buttons grid
        actions_grid = ctk.CTkFrame(actions_frame)
        actions_grid.pack(fill="x", padx=20, pady=(0, 20))

        for i in range(4):
            actions_grid.grid_columnconfigure(i, weight=1)

        quick_actions = [
            (self.t.get('add_book', 'Add Book'), "📚", self.add_book_dialog, COLORS['primary']),
            (self.t.get('add_member', 'Add Member'), "👤", self.add_member_dialog, COLORS['success']),
            (self.t.get('generate_cards', 'Generate Cards'), "🆔", self.generate_member_cards, COLORS['accent']),
            (self.t.get('backup_data', 'Backup Data'), "💾", self.backup_data, COLORS['secondary'])
        ]

        for i, (text, icon, command, color) in enumerate(quick_actions):
            btn = ctk.CTkButton(
                actions_grid,
                text=f"{icon}\n{text}",
                command=command,
                height=80,
                font=ctk.CTkFont(size=12, weight="bold"),
                fg_color=color,
                hover_color=COLORS['dark']
            )
            btn.grid(row=0, column=i, padx=5, pady=10, sticky="ew")

        # Content area with two columns
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Recent activity (left column)
        activity_frame = ctk.CTkFrame(content_frame)
        activity_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            activity_frame,
            text=f"📋 {self.t.get('recent_activity', 'Recent Activity')}",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=15, pady=(15, 10))

        self.activity_text = ctk.CTkTextbox(activity_frame, height=300, font=ctk.CTkFont(size=11))
        self.activity_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # System info (right column)
        info_frame = ctk.CTkFrame(content_frame)
        info_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            info_frame,
            text=f"ℹ️ {self.t.get('system_info', 'System Information')}",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(anchor="w", padx=15, pady=(15, 10))

        # System info content
        self.system_info_frame = ctk.CTkFrame(info_frame)
        self.system_info_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # Library hours
        working_hours = self.config.get('library_settings.working_hours', '8:00 AM - 4:00 PM')
        working_days = self.config.get('library_settings.working_days', 'Sunday - Thursday')

        info_items = [
            (f"🕒 {self.t.get('working_hours', 'Working Hours')}", working_hours),
            (f"📅 {self.t.get('working_days', 'Working Days')}", working_days),
            (f"📞 {self.t.get('phone', 'Phone')}", self.config.get('school_info.phone', 'N/A')),
            (f"📧 {self.t.get('email', 'Email')}", self.config.get('school_info.email', 'N/A')),
            (f"🌐 {self.t.get('website', 'Website')}", self.config.get('school_info.website', 'N/A'))
        ]

        for label, value in info_items:
            item_frame = ctk.CTkFrame(self.system_info_frame)
            item_frame.pack(fill="x", padx=10, pady=5)

            ctk.CTkLabel(item_frame, text=label, font=ctk.CTkFont(size=11, weight="bold")).pack(anchor="w", padx=10, pady=(8, 2))
            ctk.CTkLabel(item_frame, text=value, font=ctk.CTkFont(size=10), wraplength=200).pack(anchor="w", padx=10, pady=(0, 8))

    def create_circulation_page(self):
        """Create circulation page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["circulation"] = page

        # Page title
        title_frame = ctk.CTkFrame(page)
        title_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            title_frame,
            text="خواستن و گەڕاندنەوە",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Main content
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left side - Input form
        form_frame = ctk.CTkFrame(content_frame)
        form_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            form_frame,
            text="زانیاری ئەندام و پەرتووک",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Student ID
        ctk.CTkLabel(form_frame, text="ژمارەی ئەندام:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.student_id_entry = ctk.CTkEntry(form_frame, placeholder_text="ژمارەی ئەندام داخڵ بکە")
        self.student_id_entry.pack(fill="x", padx=20, pady=(0, 10))
        self.student_id_entry.bind("<KeyRelease>", self.on_student_id_change)

        # Book barcode
        ctk.CTkLabel(form_frame, text="بارکۆدی پەرتووک:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.book_barcode_entry = ctk.CTkEntry(form_frame, placeholder_text="بارکۆدی پەرتووک داخڵ بکە")
        self.book_barcode_entry.pack(fill="x", padx=20, pady=(0, 15))
        self.book_barcode_entry.bind("<KeyRelease>", self.on_book_barcode_change)

        # Action buttons
        button_frame = ctk.CTkFrame(form_frame)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(
            button_frame,
            text="📖 خواستنی پەرتووک",
            command=self.borrow_book,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary'],
            height=40
        ).pack(fill="x", pady=(10, 5))

        ctk.CTkButton(
            button_frame,
            text="↩️ گەڕاندنەوەی پەرتووک",
            command=self.return_book,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary'],
            height=40
        ).pack(fill="x", pady=5)

        # Right side - Information display
        info_frame = ctk.CTkFrame(content_frame)
        info_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            info_frame,
            text="زانیاری",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Student info
        self.student_info_frame = ctk.CTkFrame(info_frame)
        self.student_info_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(self.student_info_frame, text="ئەندام:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.student_info_label = ctk.CTkLabel(self.student_info_frame, text="هیچ کەسێک دیارنەکراوە")
        self.student_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Book info
        self.book_info_frame = ctk.CTkFrame(info_frame)
        self.book_info_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(self.book_info_frame, text="پەرتووک:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.book_info_label = ctk.CTkLabel(self.book_info_frame, text="هیچ پەرتووکێک دیارنەکراوە")
        self.book_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Current loans
        loans_frame = ctk.CTkFrame(info_frame)
        loans_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        ctk.CTkLabel(loans_frame, text="پەرتووکی خواستراو:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.loans_text = ctk.CTkTextbox(loans_frame, height=150)
        self.loans_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

    def create_books_page(self):
        """Create books management page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["books"] = page

        # Page title and controls
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="بەڕێوەبردنی پەرتووک",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        button_frame = ctk.CTkFrame(header_frame)
        button_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            button_frame,
            text="➕ پەرتووکی نوێ",
            command=self.add_book_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📥 هاوردەکردن",
            command=self.import_books_csv,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📤 هەناردەکردن",
            command=self.advanced_export_books,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left")

        # Search and filter
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(search_frame, text="گەڕان:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(20, 10), pady=15)

        self.books_search_entry = ctk.CTkEntry(search_frame, placeholder_text="گەڕان بە ناونیشان، نووسەر، یان بارکۆد...")
        self.books_search_entry.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=15)
        self.books_search_entry.bind("<KeyRelease>", self.search_books)

        # Books list
        list_frame = ctk.CTkFrame(page)
        list_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Create scrollable frame for books
        self.books_scroll_frame = ctk.CTkScrollableFrame(list_frame)
        self.books_scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Books will be populated here
        self.books_widgets = []

    def create_members_page(self):
        """Create members management page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["members"] = page

        # Page title and controls
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="بەڕێوەبردنی ئەندامان",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        button_frame = ctk.CTkFrame(header_frame)
        button_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            button_frame,
            text="➕ ئەندامی نوێ",
            command=self.add_member_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="🆔 کارتی ئەندامەتی",
            command=self.generate_member_cards,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📤 هەناردەکردن",
            command=self.advanced_export_members,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left")

        # Search
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(search_frame, text="گەڕان:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(20, 10), pady=15)

        self.members_search_entry = ctk.CTkEntry(search_frame, placeholder_text="گەڕان بە ناو، ژمارەی ئەندام، یان پۆل...")
        self.members_search_entry.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=15)
        self.members_search_entry.bind("<KeyRelease>", self.search_members)

        # Members list
        list_frame = ctk.CTkFrame(page)
        list_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        self.members_scroll_frame = ctk.CTkScrollableFrame(list_frame)
        self.members_scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.members_widgets = []

    def create_reports_page(self):
        """Create reports page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["reports"] = page

        # Page title
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="ڕاپۆرت و ئامار",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Content frame
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left side - Report buttons
        buttons_frame = ctk.CTkFrame(content_frame)
        buttons_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            buttons_frame,
            text="جۆرەکانی ڕاپۆرت",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        report_buttons = [
            ("📊 ئاماری گشتی", self.generate_statistics_report, COLORS['primary']),
            ("⚠️ پەرتووکی پاشکەوت", self.generate_overdue_report, COLORS['danger']),
            ("👥 ئەندامی چالاک", self.generate_active_members_report, COLORS['success']),
            ("📈 ڕاپۆرتی مانگانە", self.generate_monthly_report, COLORS['accent']),
            ("💾 یەدەگ گرتن", self.backup_data, COLORS['gray']),
            ("📤 هەناردەکردنی تەواو", self.export_complete_data, COLORS['warning'])
        ]

        for text, command, color in report_buttons:
            ctk.CTkButton(
                buttons_frame,
                text=text,
                command=command,
                fg_color=color,
                hover_color=COLORS['secondary'],
                height=40
            ).pack(fill="x", padx=20, pady=5)

        # Right side - Report display
        display_frame = ctk.CTkFrame(content_frame)
        display_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            display_frame,
            text="ڕاپۆرت",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        self.report_display = ctk.CTkTextbox(display_frame)
        self.report_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_settings_page(self):
        """Create settings page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["settings"] = page

        # Page title
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="ڕێکخستن",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Settings content
        content_frame = ctk.CTkScrollableFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))

        # School information
        school_frame = ctk.CTkFrame(content_frame)
        school_frame.pack(fill="x", pady=(0, 20))

        ctk.CTkLabel(
            school_frame,
            text="زانیاری قوتابخانە",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        school_info = [
            ("ناوی قوتابخانە:", SCHOOL_INFO['name']),
            ("کتێبخانەدار:", SCHOOL_INFO['librarian']),
            ("بەڕێوەبەر:", SCHOOL_INFO['principal']),
            ("ناونیشان:", SCHOOL_INFO['address']),
            ("تەلەفۆن:", SCHOOL_INFO['phone'])
        ]

        for label, value in school_info:
            info_frame = ctk.CTkFrame(school_frame)
            info_frame.pack(fill="x", padx=20, pady=5)

            ctk.CTkLabel(info_frame, text=label, font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)
            ctk.CTkLabel(info_frame, text=value).pack(side="left", padx=(0, 15), pady=10)

        # System settings
        system_frame = ctk.CTkFrame(content_frame)
        system_frame.pack(fill="x", pady=(0, 20))

        ctk.CTkLabel(
            system_frame,
            text="ڕێکخستنەکانی سیستەم",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Appearance mode
        appearance_frame = ctk.CTkFrame(system_frame)
        appearance_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(appearance_frame, text="شێوازی دیمەن:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

        self.appearance_mode = ctk.StringVar(value="Light")
        appearance_menu = ctk.CTkOptionMenu(
            appearance_frame,
            variable=self.appearance_mode,
            values=["Light", "Dark", "System"],
            command=self.change_appearance_mode
        )
        appearance_menu.pack(side="right", padx=15, pady=10)

        # Database info
        db_frame = ctk.CTkFrame(content_frame)
        db_frame.pack(fill="x")

        ctk.CTkLabel(
            db_frame,
            text="زانیاری بنکەی داتا",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        self.db_info_frame = ctk.CTkFrame(db_frame)
        self.db_info_frame.pack(fill="x", padx=20, pady=(0, 20))

    def show_page(self, page_name):
        """Show selected page"""
        # Hide all pages
        for page in self.pages.values():
            page.pack_forget()

        # Show selected page
        if page_name in self.pages:
            self.pages[page_name].pack(fill="both", expand=True)

        # Update navigation buttons
        for key, btn in self.nav_buttons.items():
            if key == page_name:
                btn.configure(fg_color=COLORS['accent'])
            else:
                btn.configure(fg_color=("gray75", "gray25"))

        # Refresh data if needed
        if page_name in ["dashboard", "books", "members"]:
            self.refresh_data()

    def change_appearance_mode(self, new_appearance_mode):
        """Change appearance mode"""
        ctk.set_appearance_mode(new_appearance_mode)

    # Event Handlers
    def on_student_id_change(self, event=None):
        """Handle student ID entry change"""
        student_id = self.student_id_entry.get().strip()
        if student_id:
            member = self.db.find_member_by_id(student_id)
            if member:
                self.student_info_label.configure(text=f"{member[2]} - {member[3] or 'نەدیارکراو'}")
                # Show current loans
                loans = self.db.get_member_borrowed_books(student_id)
                if loans:
                    loans_text = "پەرتووکی خواستراو:\n"
                    for loan in loans:
                        loans_text += f"• {loan[2]} (مەودا: {loan[19]})\n"
                    self.loans_text.delete("0.0", "end")
                    self.loans_text.insert("0.0", loans_text)
                else:
                    self.loans_text.delete("0.0", "end")
                    self.loans_text.insert("0.0", "هیچ پەرتووکێکی خواستراو نییە")
            else:
                self.student_info_label.configure(text="نەدۆزرایەوە!")
                self.loans_text.delete("0.0", "end")
        else:
            self.student_info_label.configure(text="هیچ کەسێک دیارنەکراوە")
            self.loans_text.delete("0.0", "end")

    def on_book_barcode_change(self, event=None):
        """Handle book barcode entry change"""
        barcode = self.book_barcode_entry.get().strip()
        if barcode:
            book = self.db.find_book_by_barcode(barcode)
            if book:
                self.book_info_label.configure(text=f"{book[2]} - {book[14]}")
            else:
                self.book_info_label.configure(text="نەدۆزرایەوە!")
        else:
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")

    def search_books(self, event=None):
        """Search books"""
        search_term = self.books_search_entry.get().strip()
        self.refresh_books_display(search_term)

    def search_members(self, event=None):
        """Search members"""
        search_term = self.members_search_entry.get().strip()
        self.refresh_members_display(search_term)

    # Action Methods
    def borrow_book(self):
        """Handle book borrowing"""
        student_id = self.student_id_entry.get().strip()
        book_barcode = self.book_barcode_entry.get().strip()

        if not student_id or not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە ژمارەی ئەندام و بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            due_date = self.db.borrow_book(book_barcode, student_id)
            messagebox.showinfo("سەرکەوتوو",
                              f"پەرتووک بە سەرکەوتوویی خواسترا\nبەرواری گەڕاندنەوە: {due_date.strftime('%Y-%m-%d')}")

            # Clear entries
            self.student_id_entry.delete(0, "end")
            self.book_barcode_entry.delete(0, "end")
            self.student_info_label.configure(text="هیچ کەسێک دیارنەکراوە")
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")
            self.loans_text.delete("0.0", "end")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە خواستنی پەرتووک: {str(e)}")

    def return_book(self):
        """Handle book returning"""
        book_barcode = self.book_barcode_entry.get().strip()

        if not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            fine_amount = self.db.return_book(book_barcode)

            if fine_amount > 0:
                messagebox.showinfo("سەرکەوتوو",
                                  f"پەرتووک بە سەرکەوتوویی گەڕێندرایەوە\nجەریمە: {fine_amount} دینار")
            else:
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی گەڕێندرایەوە")

            # Clear entries
            self.book_barcode_entry.delete(0, "end")
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە گەڕاندنەوەی پەرتووک: {str(e)}")

    def refresh_data(self):
        """Refresh all data displays"""
        try:
            # Update statistics
            stats = self.db.get_statistics()

            # Update stat cards
            if hasattr(self, 'stat_cards'):
                self.stat_cards['total_books'].configure(text=str(stats['total_books']))
                self.stat_cards['available_books'].configure(text=str(stats['available_books']))
                self.stat_cards['borrowed_books'].configure(text=str(stats['borrowed_books']))
                self.stat_cards['overdue_books'].configure(text=str(stats['overdue_books']))

            # Update sidebar stats
            stats_text = f"""کۆی پەرتووک: {stats['total_books']}
بەردەست: {stats['available_books']}
خواستراو: {stats['borrowed_books']}
پاشکەوت: {stats['overdue_books']}
کۆی ئەندام: {stats['total_members']}
چالاک: {stats['active_members']}"""

            self.stats_text.delete("0.0", "end")
            self.stats_text.insert("0.0", stats_text)

            # Update activity
            if hasattr(self, 'activity_text'):
                activity = self.get_recent_activity()
                self.activity_text.delete("0.0", "end")
                self.activity_text.insert("0.0", activity)

            # Refresh books and members displays
            self.refresh_books_display()
            self.refresh_members_display()

            # Update database info in settings
            if hasattr(self, 'db_info_frame'):
                self.update_db_info()

        except Exception as e:
            print(f"Error refreshing data: {e}")

    def get_recent_activity(self):
        """Get recent activity text"""
        try:
            # Get recent transactions
            query = '''
                SELECT t.transaction_type, b.title, m.name, t.transaction_date
                FROM transactions t
                LEFT JOIN books b ON t.book_barcode = b.barcode
                LEFT JOIN members m ON t.member_id = m.member_id
                ORDER BY t.created_at DESC
                LIMIT 10
            '''
            transactions = self.db.execute_query(query, fetch=True)

            if not transactions:
                return "هیچ چالاکیەک نییە"

            activity_text = "چالاکی نوێ:\n\n"
            for trans in transactions:
                trans_type = "خواستن" if trans[0] == "borrow" else "گەڕاندنەوە"
                activity_text += f"• {trans_type}: {trans[1] or 'نەناسراو'} - {trans[2] or 'نەناسراو'} ({trans[3]})\n"

            return activity_text

        except Exception as e:
            return f"خەڵەتی لە وەرگرتنی چالاکی: {str(e)}"

    def refresh_books_display(self, search_term=""):
        """Refresh books display"""
        try:
            # Clear existing widgets
            for widget in self.books_widgets:
                widget.destroy()
            self.books_widgets.clear()

            # Get books data
            if search_term:
                books = self.db.search_books(search_term)
            else:
                books = self.db.get_all_books()

            # Create book cards
            for book in books[:20]:  # Limit to 20 for performance
                book_card = self.create_book_card(self.books_scroll_frame, book)
                book_card.pack(fill="x", padx=10, pady=5)
                self.books_widgets.append(book_card)

        except Exception as e:
            print(f"Error refreshing books display: {e}")

    def refresh_members_display(self, search_term=""):
        """Refresh members display"""
        try:
            # Clear existing widgets
            for widget in self.members_widgets:
                widget.destroy()
            self.members_widgets.clear()

            # Get members data
            if search_term:
                members = self.db.search_members(search_term)
            else:
                members = self.db.get_all_members()

            # Create member cards
            for member in members[:20]:  # Limit to 20 for performance
                member_card = self.create_member_card(self.members_scroll_frame, member)
                member_card.pack(fill="x", padx=10, pady=5)
                self.members_widgets.append(member_card)

        except Exception as e:
            print(f"Error refreshing members display: {e}")

    def create_book_card(self, parent, book):
        """Create a book card widget"""
        card = ctk.CTkFrame(parent)

        # Book info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Title and author
        title_label = ctk.CTkLabel(info_frame, text=book[2], font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(anchor="w")

        author_label = ctk.CTkLabel(info_frame, text=f"نووسەر: {book[3]}")
        author_label.pack(anchor="w", pady=(2, 5))

        # Additional info
        details = f"بارکۆد: {book[1]} | جۆر: {book[8] or 'نەدیارکراو'} | دۆخ: {book[14]}"
        details_label = ctk.CTkLabel(info_frame, text=details, font=ctk.CTkFont(size=12))
        details_label.pack(anchor="w")

        # Status indicator
        status_color = COLORS['success'] if book[14] == 'Available' else COLORS['warning']
        status_frame = ctk.CTkFrame(card, fg_color=status_color)
        status_frame.pack(side="right", fill="y", padx=(0, 15), pady=15)

        status_label = ctk.CTkLabel(status_frame, text=book[14], font=ctk.CTkFont(weight="bold"))
        status_label.pack(padx=15, pady=10)

        return card

    def create_member_card(self, parent, member):
        """Create a member card widget"""
        card = ctk.CTkFrame(parent)

        # Member info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Name and ID
        name_label = ctk.CTkLabel(info_frame, text=member[2], font=ctk.CTkFont(size=16, weight="bold"))
        name_label.pack(anchor="w")

        id_label = ctk.CTkLabel(info_frame, text=f"ژمارە: {member[1]}")
        id_label.pack(anchor="w", pady=(2, 5))

        # Additional info
        details = f"پۆل: {member[3] or 'نەدیارکراو'} | تەلەفۆن: {member[5] or 'نەدیارکراو'}"
        details_label = ctk.CTkLabel(info_frame, text=details, font=ctk.CTkFont(size=12))
        details_label.pack(anchor="w")

        # Status indicator
        status_color = COLORS['success'] if member[11] == 'Active' else COLORS['gray']
        status_frame = ctk.CTkFrame(card, fg_color=status_color)
        status_frame.pack(side="right", fill="y", padx=(0, 15), pady=15)

        status_label = ctk.CTkLabel(status_frame, text=member[11], font=ctk.CTkFont(weight="bold"))
        status_label.pack(padx=15, pady=10)

        return card

    def update_db_info(self):
        """Update database info in settings"""
        try:
            # Clear existing info
            for widget in self.db_info_frame.winfo_children():
                widget.destroy()

            stats = self.db.get_statistics()
            db_info = [
                ("کۆی گشتی پەرتووک:", str(stats['total_books'])),
                ("کۆی گشتی ئەندام:", str(stats['total_members'])),
                ("پەرتووکی خواستراو:", str(stats['borrowed_books'])),
                ("پەرتووکی پاشکەوت:", str(stats['overdue_books'])),
                ("فایلی بنکەی داتا:", DATABASE_FILE),
                ("قەبارەی فایل:", self.get_file_size(DATABASE_FILE))
            ]

            for label, value in db_info:
                info_frame = ctk.CTkFrame(self.db_info_frame)
                info_frame.pack(fill="x", pady=2)

                ctk.CTkLabel(info_frame, text=label, font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=5)
                ctk.CTkLabel(info_frame, text=value).pack(side="left", padx=(0, 15), pady=5)

        except Exception as e:
            print(f"Error updating database info: {e}")

    def get_file_size(self, file_path):
        """Get file size in human readable format"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                for unit in ['B', 'KB', 'MB', 'GB']:
                    if size < 1024.0:
                        return f"{size:.1f} {unit}"
                    size /= 1024.0
                return f"{size:.1f} TB"
            return "نەدۆزرایەوە"
        except:
            return "نەزانراو"

    # Advanced Export Methods
    def advanced_export_books(self):
        """Show advanced export dialog for books"""
        dialog = AdvancedExportDialog(self, "books")
        self.wait_window(dialog)

        if dialog.result:
            self.perform_advanced_export("books", dialog.result)

    def advanced_export_members(self):
        """Show advanced export dialog for members"""
        dialog = AdvancedExportDialog(self, "members")
        self.wait_window(dialog)

        if dialog.result:
            self.perform_advanced_export("members", dialog.result)

    def perform_advanced_export(self, export_type, options):
        """Perform advanced export with options"""
        try:
            # Show progress
            progress_dialog = ctk.CTkToplevel(self)
            progress_dialog.title("هەناردەکردن...")
            progress_dialog.geometry("400x150")
            progress_dialog.transient(self)
            progress_dialog.grab_set()

            progress_label = ctk.CTkLabel(progress_dialog, text="هەناردەکردن لە جێبەجێکردندایە...")
            progress_label.pack(pady=20)

            progress_bar = ctk.CTkProgressBar(progress_dialog)
            progress_bar.pack(pady=10, padx=20, fill="x")
            progress_bar.set(0)

            # Update progress
            self.update()
            progress_bar.set(0.3)

            # Get data based on filters
            if export_type == "books":
                data = self.get_filtered_books_data(options)
            else:
                data = self.get_filtered_members_data(options)

            progress_bar.set(0.6)

            # Export data
            if options['format'] == 'excel':
                self.export_to_excel_advanced(data, options, export_type)
            else:
                self.export_to_csv_advanced(data, options, export_type)

            progress_bar.set(1.0)
            progress_dialog.destroy()

            messagebox.showinfo("سەرکەوتوو", f"داتا بە سەرکەوتوویی هەناردەکرا بۆ:\n{options['file_path']}")

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.destroy()
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def get_filtered_books_data(self, options):
        """Get filtered books data"""
        query = "SELECT * FROM books WHERE 1=1"
        params = []

        # Date range filter
        if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
            query += " AND date_added BETWEEN ? AND ?"
            params.extend([options['from_date'].strftime('%Y-%m-%d'), options['to_date'].strftime('%Y-%m-%d')])

        # Status filter
        if options.get('status_filter') and options['status_filter'] != 'all':
            query += " AND status = ?"
            params.append(options['status_filter'])

        query += " ORDER BY title"

        return self.db.execute_query(query, params, fetch=True)

    def get_filtered_members_data(self, options):
        """Get filtered members data"""
        query = "SELECT * FROM members WHERE 1=1"
        params = []

        # Date range filter
        if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
            query += " AND registration_date BETWEEN ? AND ?"
            params.extend([options['from_date'].strftime('%Y-%m-%d'), options['to_date'].strftime('%Y-%m-%d')])

        query += " ORDER BY name"

        return self.db.execute_query(query, params, fetch=True)

    def export_to_excel_advanced(self, data, options, export_type):
        """Export to Excel with advanced options"""
        with pd.ExcelWriter(options['file_path'], engine='openpyxl') as writer:
            # Main data sheet
            if export_type == "books":
                columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                          'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                          'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added',
                          'Created At', 'Updated At']
                sheet_name = 'پەرتووک'
            else:
                columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                          'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                          'Registration Date', 'Status', 'Photo Path', 'Created At', 'Updated At']
                sheet_name = 'ئەندامان'

            df = pd.DataFrame(data, columns=columns)
            df.to_excel(writer, sheet_name=sheet_name, index=False)

            # Add statistics sheet if requested
            if options.get('include_stats'):
                stats = self.db.get_statistics()
                stats_data = [
                    ['کۆی گشتی پەرتووک', stats['total_books']],
                    ['پەرتووکی بەردەست', stats['available_books']],
                    ['پەرتووکی خواستراو', stats['borrowed_books']],
                    ['کۆی گشتی ئەندام', stats['total_members']],
                    ['ئەندامی چالاک', stats['active_members']],
                    ['پەرتووکی پاشکەوت', stats['overdue_books']]
                ]
                stats_df = pd.DataFrame(stats_data, columns=['بابەت', 'ژمارە'])
                stats_df.to_excel(writer, sheet_name='ئامار', index=False)

            # Add summary sheet if requested
            if options.get('include_summary'):
                summary_data = [
                    ['بەرواری هەناردەکردن', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                    ['جۆری هەناردەکردن', export_type],
                    ['ژمارەی تۆمار', len(data)],
                    ['فلتەری بەکارهاتوو', 'بەڵێ' if options.get('use_date_range') else 'نەخێر']
                ]

                if options.get('use_date_range'):
                    summary_data.extend([
                        ['لە بەرواری', options['from_date'].strftime('%Y-%m-%d')],
                        ['تا بەرواری', options['to_date'].strftime('%Y-%m-%d')]
                    ])

                summary_df = pd.DataFrame(summary_data, columns=['بابەت', 'نرخ'])
                summary_df.to_excel(writer, sheet_name='پوختە', index=False)

    def export_to_csv_advanced(self, data, options, export_type):
        """Export to CSV with advanced options"""
        if export_type == "books":
            columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                      'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                      'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added',
                      'Created At', 'Updated At']
        else:
            columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                      'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                      'Registration Date', 'Status', 'Photo Path', 'Created At', 'Updated At']

        df = pd.DataFrame(data, columns=columns)
        df.to_csv(options['file_path'], index=False, encoding='utf-8-sig')

    # Dialog Methods
    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("زیادکردنی پەرتووکی نوێ")
        dialog.geometry("600x700")
        dialog.transient(self)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (self.winfo_x() + (self.winfo_width() // 2)) - (dialog.winfo_width() // 2)
        y = (self.winfo_y() + (self.winfo_height() // 2)) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create scrollable form
        scroll_frame = ctk.CTkScrollableFrame(dialog)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        ctk.CTkLabel(scroll_frame, text="زیادکردنی پەرتووکی نوێ",
                    font=ctk.CTkFont(size=20, weight="bold")).pack(pady=(0, 20))

        # Form fields
        fields = {}

        form_data = [
            ("barcode", "بارکۆد *", True),
            ("title", "ناونیشان *", True),
            ("author", "نووسەر *", True),
            ("ddc_number", "ژمارەی DDC", False),
            ("author_code", "کۆدی نووسەر", False),
            ("isbn", "ISBN", False),
            ("category", "جۆر", False),
            ("publisher", "دەرچوون", False),
            ("publication_year", "ساڵی دەرچوون", False),
            ("pages", "ژمارەی لاپەڕە", False),
            ("language", "زمان", False),
            ("location", "شوێن", False)
        ]

        for field_name, label_text, required in form_data:
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", pady=5)

            ctk.CTkLabel(field_frame, text=label_text, font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

            entry = ctk.CTkEntry(field_frame, placeholder_text=f"{label_text} داخڵ بکە")
            entry.pack(fill="x", padx=15, pady=(0, 15))

            if field_name == "language":
                entry.insert(0, "کوردی")

            fields[field_name] = entry

        # Summary field (text area)
        summary_frame = ctk.CTkFrame(scroll_frame)
        summary_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(summary_frame, text="پوختە", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        summary_text = ctk.CTkTextbox(summary_frame, height=80)
        summary_text.pack(fill="x", padx=15, pady=(0, 15))
        fields["summary"] = summary_text

        # Buttons
        button_frame = ctk.CTkFrame(scroll_frame)
        button_frame.pack(fill="x", pady=20)

        def save_book():
            try:
                # Validate required fields
                required_fields = ["barcode", "title", "author"]
                for field in required_fields:
                    if not fields[field].get().strip():
                        messagebox.showwarning("ئاگاداری", f"تکایە {field} پڕ بکەرەوە")
                        return

                # Prepare data
                book_data = [
                    fields["barcode"].get().strip(),
                    fields["title"].get().strip(),
                    fields["author"].get().strip(),
                    fields["ddc_number"].get().strip(),
                    fields["author_code"].get().strip(),
                    f"{fields['ddc_number'].get().strip()} {fields['author_code'].get().strip()}".strip(),
                    fields["isbn"].get().strip(),
                    fields["category"].get().strip(),
                    fields["publisher"].get().strip(),
                    int(fields["publication_year"].get().strip()) if fields["publication_year"].get().strip().isdigit() else None,
                    int(fields["pages"].get().strip()) if fields["pages"].get().strip().isdigit() else None,
                    fields["language"].get().strip() or "کوردی",
                    fields["summary"].get("0.0", "end").strip(),
                    fields["location"].get().strip()
                ]

                self.db.add_book(book_data)
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی پەرتووک: {str(e)}")

        ctk.CTkButton(button_frame, text="پاشەکەوتکردن", command=save_book,
                     fg_color=COLORS['success']).pack(side="left", padx=(15, 10), pady=15)
        ctk.CTkButton(button_frame, text="پاشگەزبوونەوە", command=dialog.destroy,
                     fg_color=COLORS['gray']).pack(side="left", padx=(0, 15), pady=15)

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("زیادکردنی ئەندامی نوێ")
        dialog.geometry("600x600")
        dialog.transient(self)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (self.winfo_x() + (self.winfo_width() // 2)) - (dialog.winfo_width() // 2)
        y = (self.winfo_y() + (self.winfo_height() // 2)) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create scrollable form
        scroll_frame = ctk.CTkScrollableFrame(dialog)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        ctk.CTkLabel(scroll_frame, text="زیادکردنی ئەندامی نوێ",
                    font=ctk.CTkFont(size=20, weight="bold")).pack(pady=(0, 20))

        # Form fields
        fields = {}

        form_data = [
            ("member_id", "ژمارەی ئەندام *", True),
            ("name", "ناو *", True),
            ("class_grade", "پۆل", False),
            ("national_id", "ژمارەی ناسنامە", False),
            ("phone", "تەلەفۆن", False),
            ("guardian_name", "ناوی سەرپەرشتیار", False),
            ("guardian_phone", "تەلەفۆنی سەرپەرشتیار", False),
            ("address", "ناونیشان", False),
            ("email", "ئیمەیڵ", False)
        ]

        for field_name, label_text, required in form_data:
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", pady=5)

            ctk.CTkLabel(field_frame, text=label_text, font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

            entry = ctk.CTkEntry(field_frame, placeholder_text=f"{label_text} داخڵ بکە")
            entry.pack(fill="x", padx=15, pady=(0, 15))

            fields[field_name] = entry

        # Buttons
        button_frame = ctk.CTkFrame(scroll_frame)
        button_frame.pack(fill="x", pady=20)

        def save_member():
            try:
                # Validate required fields
                if not fields["member_id"].get().strip() or not fields["name"].get().strip():
                    messagebox.showwarning("ئاگاداری", "تکایە خانەکانی پێویست پڕ بکەرەوە")
                    return

                # Prepare data
                member_data = [
                    fields["member_id"].get().strip(),
                    fields["name"].get().strip(),
                    fields["class_grade"].get().strip(),
                    fields["national_id"].get().strip(),
                    fields["phone"].get().strip(),
                    fields["guardian_name"].get().strip(),
                    fields["guardian_phone"].get().strip(),
                    fields["address"].get().strip(),
                    fields["email"].get().strip()
                ]

                self.db.add_member(member_data)
                messagebox.showinfo("سەرکەوتوو", "ئەندام بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی ئەندام: {str(e)}")

        ctk.CTkButton(button_frame, text="پاشەکەوتکردن", command=save_member,
                     fg_color=COLORS['success']).pack(side="left", padx=(15, 10), pady=15)
        ctk.CTkButton(button_frame, text="پاشگەزبوونەوە", command=dialog.destroy,
                     fg_color=COLORS['gray']).pack(side="left", padx=(0, 15), pady=15)

    # Import Methods
    def import_books_csv(self):
        """Import books from CSV"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ پەرتووک",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_books_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} پەرتووک هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    def import_members_csv(self):
        """Import members from CSV"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ ئەندام",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_members_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} ئەندام هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    # Report Methods
    def generate_statistics_report(self):
        """Generate statistics report"""
        try:
            stats = self.db.get_statistics()

            report = f"""ڕاپۆرتی ئاماری گشتی
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

پەرتووک:
  کۆی گشتی: {stats['total_books']}
  بەردەست: {stats['available_books']}
  خواستراو: {stats['borrowed_books']}
  پاشکەوت: {stats['overdue_books']}

ئەندام:
  کۆی گشتی: {stats['total_members']}
  چالاک: {stats['active_members']}

ڕێژەکان:"""

            if stats['total_books'] > 0:
                available_percent = (stats['available_books'] / stats['total_books']) * 100
                borrowed_percent = (stats['borrowed_books'] / stats['total_books']) * 100
                report += f"""
  پەرتووکی بەردەست: {available_percent:.1f}%
  پەرتووکی خواستراو: {borrowed_percent:.1f}%"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_overdue_report(self):
        """Generate overdue books report"""
        try:
            overdue_books = self.db.get_overdue_books()

            report = f"""ڕاپۆرتی پەرتووکی پاشکەوت
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
کۆی گشتی: {len(overdue_books)} پەرتووک
{'='*50}

"""

            if overdue_books:
                for book in overdue_books:
                    days_overdue = (datetime.now().date() - datetime.strptime(book[4], '%Y-%m-%d').date()).days
                    fine = days_overdue * 250
                    report += f"""پەرتووک: {book[0]}
نووسەر: {book[1]}
ئەندام: {book[2]} ({book[3]})
بەرواری گەڕاندنەوە: {book[4]}
ڕۆژی پاشکەوت: {days_overdue}
جەریمە: {fine} دینار
{'-'*30}
"""
            else:
                report += "هیچ پەرتووکێکی پاشکەوت نییە."

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_active_members_report(self):
        """Generate active members report"""
        try:
            query = '''
                SELECT DISTINCT m.member_id, m.name, m.class_grade, COUNT(t.id) as borrowed_count
                FROM members m
                JOIN transactions t ON m.member_id = t.member_id
                WHERE t.transaction_type = 'borrow' AND t.return_date IS NULL
                GROUP BY m.member_id, m.name, m.class_grade
                ORDER BY borrowed_count DESC
            '''
            active_members = self.db.execute_query(query, fetch=True)

            report = f"""ڕاپۆرتی ئەندامی چالاک
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
کۆی گشتی: {len(active_members)} ئەندام
{'='*50}

"""

            if active_members:
                for member in active_members:
                    report += f"""ئەندام: {member[1]}
ژمارە: {member[0]}
پۆل: {member[2] or 'نەدیارکراو'}
پەرتووکی خواستراو: {member[3]}
{'-'*30}
"""
            else:
                report += "هیچ ئەندامێکی چالاک نییە."

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_monthly_report(self):
        """Generate monthly report"""
        try:
            current_month = datetime.now().strftime('%Y-%m')

            # Get monthly statistics
            query = '''
                SELECT
                    COUNT(CASE WHEN transaction_type = 'borrow' THEN 1 END) as borrows,
                    COUNT(CASE WHEN transaction_type = 'return' THEN 1 END) as returns
                FROM transactions
                WHERE strftime('%Y-%m', transaction_date) = ?
            '''
            monthly_stats = self.db.execute_query(query, (current_month,), fetch=True)[0]

            report = f"""ڕاپۆرتی مانگانە - {current_month}
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

چالاکی مانگ:
  خواستنی پەرتووک: {monthly_stats[0]}
  گەڕاندنەوەی پەرتووک: {monthly_stats[1]}

ئاماری ئێستا:"""

            stats = self.db.get_statistics()
            report += f"""
  کۆی پەرتووک: {stats['total_books']}
  پەرتووکی خواستراو: {stats['borrowed_books']}
  پەرتووکی پاشکەوت: {stats['overdue_books']}
  کۆی ئەندام: {stats['total_members']}
  ئەندامی چالاک: {stats['active_members']}"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_member_cards(self):
        """Generate member ID cards"""
        try:
            result = messagebox.askyesno("دڵنیایی", "ئایا دەتەوێت کارتی ئەندامەتی بۆ هەموو ئەندامان دروست بکەیت؟")
            if result:
                # Show progress
                progress_dialog = ctk.CTkToplevel(self)
                progress_dialog.title("دروستکردنی کارت...")
                progress_dialog.geometry("400x150")
                progress_dialog.transient(self)
                progress_dialog.grab_set()

                progress_label = ctk.CTkLabel(progress_dialog, text="کارتەکان دروست دەکرێن...")
                progress_label.pack(pady=20)

                progress_bar = ctk.CTkProgressBar(progress_dialog)
                progress_bar.pack(pady=10, padx=20, fill="x")
                progress_bar.set(0)

                self.update()

                # Generate cards in background
                def generate_cards():
                    try:
                        card_paths = self.id_generator.generate_all_member_cards()
                        progress_bar.set(0.8)

                        if card_paths:
                            html_file = self.id_generator.create_cards_html_page(card_paths)
                            progress_bar.set(1.0)

                            progress_dialog.destroy()

                            messagebox.showinfo("سەرکەوتوو",
                                              f"بە سەرکەوتوویی {len(card_paths)} کارت دروستکرا\n"
                                              f"فایلی HTML: {html_file}")

                            if messagebox.askyesno("کردنەوە", "ئایا دەتەوێت فایلی HTML بکەیتەوە؟"):
                                webbrowser.open(html_file)
                        else:
                            progress_dialog.destroy()
                            messagebox.showwarning("ئاگاداری", "هیچ کارتێک دروست نەکرا")
                    except Exception as e:
                        progress_dialog.destroy()
                        messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی کارت: {str(e)}")

                # Run in thread to prevent UI freezing
                threading.Thread(target=generate_cards, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی کارت: {str(e)}")

    def export_complete_data(self):
        """Export complete data"""
        file_path = filedialog.asksaveasfilename(
            title="پاشەکەوتکردنی ڕاپۆرتی تەواو",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.data_manager.export_complete_report_to_excel(file_path)
                messagebox.showinfo("سەرکەوتوو", f"ڕاپۆرتی تەواو بە سەرکەوتوویی هەناردەکرا بۆ:\n{file_path}")
            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def backup_data(self):
        """Create data backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = os.path.join(BACKUP_FOLDER, f"library_backup_{timestamp}")

            shutil.make_archive(backup_filename, 'zip', root_dir='.')

            messagebox.showinfo("سەرکەوتوو", f"یەدەگ بە سەرکەوتوویی دروستکرا:\n{backup_filename}.zip")

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی یەدەگ: {str(e)}")

# ==============================================================================
# Main Execution
# ==============================================================================

    def create_enhanced_circulation_page(self):
        """Create enhanced circulation page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["circulation"] = page

        # Page header
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        ctk.CTkLabel(
            header_frame,
            text=f"🔄 {self.t.get('circulation', 'Circulation')}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Main content with two columns
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left column - Input forms
        input_frame = ctk.CTkFrame(content_frame)
        input_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            input_frame,
            text=f"📝 {self.t.get('transaction_form', 'Transaction Form')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Member ID input
        member_frame = ctk.CTkFrame(input_frame)
        member_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(member_frame, text=f"{self.t.get('member_id', 'Member ID')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.member_id_entry = ctk.CTkEntry(
            member_frame,
            placeholder_text=self.t.get('enter_member_id', 'Enter member ID'),
            font=ctk.CTkFont(size=14)
        )
        self.member_id_entry.pack(fill="x", padx=15, pady=(0, 15))
        self.member_id_entry.bind("<KeyRelease>", self.on_member_id_change)

        # Book barcode input
        book_frame = ctk.CTkFrame(input_frame)
        book_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(book_frame, text=f"{self.t.get('barcode', 'Barcode')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.book_barcode_entry = ctk.CTkEntry(
            book_frame,
            placeholder_text=self.t.get('enter_barcode', 'Enter book barcode'),
            font=ctk.CTkFont(size=14)
        )
        self.book_barcode_entry.pack(fill="x", padx=15, pady=(0, 15))
        self.book_barcode_entry.bind("<KeyRelease>", self.on_book_barcode_change)

        # Action buttons
        buttons_frame = ctk.CTkFrame(input_frame)
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(
            buttons_frame,
            text=f"📖 {self.t.get('borrow_book', 'Borrow Book')}",
            command=self.borrow_book,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(fill="x", padx=15, pady=(15, 5))

        ctk.CTkButton(
            buttons_frame,
            text=f"↩️ {self.t.get('return_book', 'Return Book')}",
            command=self.return_book,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(fill="x", padx=15, pady=(5, 15))

        # Right column - Information display
        info_frame = ctk.CTkFrame(content_frame)
        info_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            info_frame,
            text=f"ℹ️ {self.t.get('information', 'Information')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Member info display
        self.member_info_frame = ctk.CTkFrame(info_frame)
        self.member_info_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(self.member_info_frame, text=f"{self.t.get('member_info', 'Member Information')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.member_info_label = ctk.CTkLabel(
            self.member_info_frame,
            text=self.t.get('no_member_selected', 'No member selected'),
            font=ctk.CTkFont(size=12),
            wraplength=300
        )
        self.member_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Book info display
        self.book_info_frame = ctk.CTkFrame(info_frame)
        self.book_info_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(self.book_info_frame, text=f"{self.t.get('book_info', 'Book Information')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.book_info_label = ctk.CTkLabel(
            self.book_info_frame,
            text=self.t.get('no_book_selected', 'No book selected'),
            font=ctk.CTkFont(size=12),
            wraplength=300
        )
        self.book_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Current loans display
        loans_frame = ctk.CTkFrame(info_frame)
        loans_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        ctk.CTkLabel(loans_frame, text=f"{self.t.get('current_loans', 'Current Loans')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.loans_text = ctk.CTkTextbox(loans_frame, height=200, font=ctk.CTkFont(size=11))
        self.loans_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

    def create_enhanced_books_page(self):
        """Create enhanced books management page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["books"] = page

        # Page header with actions
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        ctk.CTkLabel(
            header_frame,
            text=f"📚 {self.t.get('books', 'Books Management')}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        actions_frame = ctk.CTkFrame(header_frame)
        actions_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            actions_frame,
            text=f"➕ {self.t.get('add_book', 'Add Book')}",
            command=self.add_book_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            actions_frame,
            text=f"📥 {self.t.get('import', 'Import')}",
            command=self.import_books_csv,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            actions_frame,
            text=f"📤 {self.t.get('export', 'Export')}",
            command=self.export_books_advanced,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left")

        # Search and filter section
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 15))

        # Search input
        search_input_frame = ctk.CTkFrame(search_frame)
        search_input_frame.pack(fill="x", padx=20, pady=15)

        ctk.CTkLabel(search_input_frame, text=f"🔍 {self.t.get('search', 'Search')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))

        self.books_search_var = ctk.StringVar()
        self.books_search_entry = ctk.CTkEntry(
            search_input_frame,
            textvariable=self.books_search_var,
            placeholder_text=self.t.get('search_books', 'Search by title, author, or barcode...'),
            width=400,
            font=ctk.CTkFont(size=14)
        )
        self.books_search_entry.pack(side="left", padx=(0, 10), fill="x", expand=True)
        self.books_search_var.trace("w", self.on_books_search_change)

        # Filter options
        filter_frame = ctk.CTkFrame(search_frame)
        filter_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(filter_frame, text=f"🏷️ {self.t.get('filter', 'Filter')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))

        self.books_status_filter = ctk.StringVar(value="all")
        status_menu = ctk.CTkOptionMenu(
            filter_frame,
            variable=self.books_status_filter,
            values=["all", "Available", "Borrowed"],
            command=self.on_books_filter_change,
            width=120
        )
        status_menu.pack(side="left", padx=(0, 10))

        self.books_category_filter = ctk.StringVar(value="all")
        category_menu = ctk.CTkOptionMenu(
            filter_frame,
            variable=self.books_category_filter,
            values=["all"],  # Will be populated dynamically
            command=self.on_books_filter_change,
            width=150
        )
        category_menu.pack(side="left", padx=(0, 10))

        # Books display area
        books_display_frame = ctk.CTkFrame(page)
        books_display_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Books list with scrollable frame
        self.books_list_frame = ctk.CTkScrollableFrame(books_display_frame, height=400)
        self.books_list_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Initialize books display
        self.books_widgets = []
        self.load_books_display()

    def create_enhanced_members_page(self):
        """Create enhanced members management page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["members"] = page

        # Page header with actions
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        ctk.CTkLabel(
            header_frame,
            text=f"👥 {self.t.get('members', 'Members Management')}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        actions_frame = ctk.CTkFrame(header_frame)
        actions_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            actions_frame,
            text=f"➕ {self.t.get('add_member', 'Add Member')}",
            command=self.add_member_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            actions_frame,
            text=f"🆔 {self.t.get('generate_cards', 'Generate Cards')}",
            command=self.generate_member_cards,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            actions_frame,
            text=f"📤 {self.t.get('export', 'Export')}",
            command=self.export_members_advanced,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary'],
            font=ctk.CTkFont(weight="bold")
        ).pack(side="left")

        # Search and filter section
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 15))

        # Search input
        search_input_frame = ctk.CTkFrame(search_frame)
        search_input_frame.pack(fill="x", padx=20, pady=15)

        ctk.CTkLabel(search_input_frame, text=f"🔍 {self.t.get('search', 'Search')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))

        self.members_search_var = ctk.StringVar()
        self.members_search_entry = ctk.CTkEntry(
            search_input_frame,
            textvariable=self.members_search_var,
            placeholder_text=self.t.get('search_members', 'Search by name, member ID, or class...'),
            width=400,
            font=ctk.CTkFont(size=14)
        )
        self.members_search_entry.pack(side="left", padx=(0, 10), fill="x", expand=True)
        self.members_search_var.trace("w", self.on_members_search_change)

        # Filter options
        filter_frame = ctk.CTkFrame(search_frame)
        filter_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(filter_frame, text=f"🏷️ {self.t.get('filter', 'Filter')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(0, 10))

        self.members_status_filter = ctk.StringVar(value="all")
        status_menu = ctk.CTkOptionMenu(
            filter_frame,
            variable=self.members_status_filter,
            values=["all", "Active", "Inactive"],
            command=self.on_members_filter_change,
            width=120
        )
        status_menu.pack(side="left", padx=(0, 10))

        self.members_type_filter = ctk.StringVar(value="all")
        type_menu = ctk.CTkOptionMenu(
            filter_frame,
            variable=self.members_type_filter,
            values=["all", "Student", "Teacher", "Staff"],
            command=self.on_members_filter_change,
            width=120
        )
        type_menu.pack(side="left", padx=(0, 10))

        # Members display area
        members_display_frame = ctk.CTkFrame(page)
        members_display_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Members list with scrollable frame
        self.members_list_frame = ctk.CTkScrollableFrame(members_display_frame, height=400)
        self.members_list_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Initialize members display
        self.members_widgets = []
        self.load_members_display()

    def create_enhanced_reports_page(self):
        """Create enhanced reports and analytics page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["reports"] = page

        # Page header
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        ctk.CTkLabel(
            header_frame,
            text=f"📊 {self.t.get('reports', 'Reports & Analytics')}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Main content with two columns
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left column - Report types
        reports_frame = ctk.CTkFrame(content_frame)
        reports_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            reports_frame,
            text=f"📋 {self.t.get('report_types', 'Report Types')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Report buttons
        report_buttons = [
            (f"📊 {self.t.get('statistics_report', 'Statistics Report')}", self.generate_statistics_report, COLORS['primary']),
            (f"⚠️ {self.t.get('overdue_report', 'Overdue Books')}", self.generate_overdue_report, COLORS['danger']),
            (f"👥 {self.t.get('active_members_report', 'Active Members')}", self.generate_active_members_report, COLORS['success']),
            (f"📈 {self.t.get('monthly_report', 'Monthly Report')}", self.generate_monthly_report, COLORS['accent']),
            (f"📚 {self.t.get('popular_books', 'Popular Books')}", self.generate_popular_books_report, COLORS['secondary']),
            (f"💰 {self.t.get('fines_report', 'Fines Report')}", self.generate_fines_report, COLORS['warning']),
            (f"📤 {self.t.get('export_complete', 'Export All Data')}", self.export_complete_data, COLORS['gray']),
            (f"💾 {self.t.get('backup_data', 'Backup System')}", self.backup_data, COLORS['dark'])
        ]

        for text, command, color in report_buttons:
            btn = ctk.CTkButton(
                reports_frame,
                text=text,
                command=command,
                height=45,
                font=ctk.CTkFont(size=13, weight="bold"),
                fg_color=color,
                hover_color=COLORS['secondary']
            )
            btn.pack(fill="x", padx=20, pady=5)

        # Right column - Report display
        display_frame = ctk.CTkFrame(content_frame)
        display_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            display_frame,
            text=f"📄 {self.t.get('report_display', 'Report Display')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Report display area
        self.report_display = ctk.CTkTextbox(
            display_frame,
            height=500,
            font=ctk.CTkFont(family="Consolas", size=11)
        )
        self.report_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Report actions
        report_actions_frame = ctk.CTkFrame(display_frame)
        report_actions_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(
            report_actions_frame,
            text=f"💾 {self.t.get('save_report', 'Save Report')}",
            command=self.save_current_report,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(0, 10), pady=10)

        ctk.CTkButton(
            report_actions_frame,
            text=f"🖨️ {self.t.get('print_report', 'Print Report')}",
            command=self.print_current_report,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left", padx=(0, 10), pady=10)

        ctk.CTkButton(
            report_actions_frame,
            text=f"🔄 {self.t.get('refresh_report', 'Refresh')}",
            command=self.refresh_current_report,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left", pady=10)

        # Initialize with welcome message
        welcome_msg = f"""
{self.t.get('welcome_to_reports', 'Welcome to Reports & Analytics')}

{self.t.get('select_report_type', 'Select a report type from the left panel to generate reports.')}

{self.t.get('available_reports', 'Available Reports')}:
• {self.t.get('statistics_report', 'Statistics Report')} - {self.t.get('overall_library_stats', 'Overall library statistics')}
• {self.t.get('overdue_report', 'Overdue Books')} - {self.t.get('books_past_due', 'Books past their due date')}
• {self.t.get('active_members_report', 'Active Members')} - {self.t.get('members_with_loans', 'Members with current loans')}
• {self.t.get('monthly_report', 'Monthly Report')} - {self.t.get('current_month_activity', 'Current month activity')}
• {self.t.get('popular_books', 'Popular Books')} - {self.t.get('most_borrowed_books', 'Most borrowed books')}
• {self.t.get('fines_report', 'Fines Report')} - {self.t.get('outstanding_fines', 'Outstanding fines')}

{self.t.get('report_features', 'Features')}:
• {self.t.get('real_time_data', 'Real-time data')}
• {self.t.get('export_options', 'Export to various formats')}
• {self.t.get('print_support', 'Print support')}
• {self.t.get('save_reports', 'Save reports for later')}
"""

        self.report_display.insert("0.0", welcome_msg)
        self.current_report_type = None
        self.current_report_data = None

    def create_enhanced_settings_page(self):
        """Create enhanced settings and configuration page"""
        page = ctk.CTkScrollableFrame(self.main_frame)
        self.pages["settings"] = page

        # Page header
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 15))

        ctk.CTkLabel(
            header_frame,
            text=f"⚙️ {self.t.get('settings', 'Settings & Configuration')}",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # School Information Section
        school_frame = ctk.CTkFrame(page)
        school_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(
            school_frame,
            text=f"🏫 {self.t.get('school_information', 'School Information')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        # School info fields
        school_fields = [
            ('name', self.t.get('school_name', 'School Name')),
            ('librarian', self.t.get('librarian', 'Librarian')),
            ('principal', self.t.get('principal', 'Principal')),
            ('address', self.t.get('address', 'Address')),
            ('phone', self.t.get('phone', 'Phone')),
            ('email', self.t.get('email', 'Email')),
            ('website', self.t.get('website', 'Website'))
        ]

        self.school_entries = {}
        for field_key, field_label in school_fields:
            field_frame = ctk.CTkFrame(school_frame)
            field_frame.pack(fill="x", padx=20, pady=5)

            ctk.CTkLabel(field_frame, text=f"{field_label}:",
                        font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

            entry = ctk.CTkEntry(field_frame, width=300, font=ctk.CTkFont(size=12))
            entry.pack(side="right", padx=15, pady=10)
            entry.insert(0, self.config.get(f'school_info.{field_key}', ''))
            self.school_entries[field_key] = entry

        # Library Settings Section
        library_frame = ctk.CTkFrame(page)
        library_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(
            library_frame,
            text=f"📚 {self.t.get('library_settings', 'Library Settings')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        # Library settings fields
        library_fields = [
            ('loan_period_days', self.t.get('loan_period_days', 'Loan Period (Days)'), 'number'),
            ('max_books_per_student', self.t.get('max_books_per_student', 'Max Books per Student'), 'number'),
            ('fine_per_day', self.t.get('fine_per_day', 'Fine per Day'), 'number'),
            ('currency', self.t.get('currency', 'Currency'), 'text'),
            ('working_hours', self.t.get('working_hours', 'Working Hours'), 'text'),
            ('working_days', self.t.get('working_days', 'Working Days'), 'text')
        ]

        self.library_entries = {}
        for field_key, field_label, field_type in library_fields:
            field_frame = ctk.CTkFrame(library_frame)
            field_frame.pack(fill="x", padx=20, pady=5)

            ctk.CTkLabel(field_frame, text=f"{field_label}:",
                        font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

            entry = ctk.CTkEntry(field_frame, width=200, font=ctk.CTkFont(size=12))
            entry.pack(side="right", padx=15, pady=10)
            entry.insert(0, str(self.config.get(f'library_settings.{field_key}', '')))
            self.library_entries[field_key] = entry

        # Appearance Settings Section
        appearance_frame = ctk.CTkFrame(page)
        appearance_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(
            appearance_frame,
            text=f"🎨 {self.t.get('appearance_settings', 'Appearance Settings')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        # Appearance mode
        mode_frame = ctk.CTkFrame(appearance_frame)
        mode_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(mode_frame, text=f"{self.t.get('appearance_mode', 'Appearance Mode')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

        self.appearance_mode_var = ctk.StringVar(value=self.config.get('appearance_mode', 'light'))
        appearance_menu = ctk.CTkOptionMenu(
            mode_frame,
            variable=self.appearance_mode_var,
            values=["light", "dark", "system"],
            command=self.change_appearance_mode,
            width=150
        )
        appearance_menu.pack(side="right", padx=15, pady=10)

        # Color theme
        theme_frame = ctk.CTkFrame(appearance_frame)
        theme_frame.pack(fill="x", padx=20, pady=(5, 15))

        ctk.CTkLabel(theme_frame, text=f"{self.t.get('color_theme', 'Color Theme')}:",
                    font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

        self.color_theme_var = ctk.StringVar(value=self.config.get('color_theme', 'blue'))
        theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            variable=self.color_theme_var,
            values=["blue", "green", "dark-blue"],
            command=self.change_color_theme,
            width=150
        )
        theme_menu.pack(side="right", padx=15, pady=10)

        # System Information Section
        system_frame = ctk.CTkFrame(page)
        system_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(
            system_frame,
            text=f"💻 {self.t.get('system_information', 'System Information')}",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 15))

        # System info display
        self.system_info_display = ctk.CTkFrame(system_frame)
        self.system_info_display.pack(fill="x", padx=20, pady=(0, 20))

        self.update_system_info()

        # Action buttons
        actions_frame = ctk.CTkFrame(page)
        actions_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(
            actions_frame,
            text=f"💾 {self.t.get('save_settings', 'Save Settings')}",
            command=self.save_settings,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(20, 10), pady=20)

        ctk.CTkButton(
            actions_frame,
            text=f"🔄 {self.t.get('reset_settings', 'Reset to Defaults')}",
            command=self.reset_settings,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['warning'],
            hover_color=COLORS['danger']
        ).pack(side="left", padx=(0, 10), pady=20)

        ctk.CTkButton(
            actions_frame,
            text=f"📥 {self.t.get('import_settings', 'Import Settings')}",
            command=self.import_settings,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left", padx=(0, 10), pady=20)

        ctk.CTkButton(
            actions_frame,
            text=f"📤 {self.t.get('export_settings', 'Export Settings')}",
            command=self.export_settings,
            height=50,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left", pady=20)

    def show_page(self, page_name):
        """Show selected page"""
        # Hide all pages
        for page in self.pages.values():
            page.pack_forget()

        # Show selected page
        if page_name in self.pages:
            self.pages[page_name].pack(fill="both", expand=True)

        # Update navigation buttons
        for key, btn in self.nav_buttons.items():
            if key == page_name:
                btn.configure(fg_color=COLORS['accent'])
            else:
                btn.configure(fg_color=("gray75", "gray25"))

        # Refresh data if needed
        if page_name in ["dashboard", "books", "members"]:
            self.refresh_data()

    def refresh_data(self):
        """Refresh all data displays"""
        try:
            # Get statistics
            books = self.db.get_all_books()
            members = self.db.get_all_members()

            # Calculate stats
            total_books = len(books)
            available_books = len([b for b in books if b[14] == 'Available'])
            borrowed_books = total_books - available_books
            overdue_books = 0  # Placeholder

            total_members = len(members)
            active_members = len([m for m in members if m[15] == 'Active'])
            new_this_month = 0  # Placeholder

            # Update dashboard stat cards
            if hasattr(self, 'stat_cards'):
                self.stat_cards['total_books'].configure(text=str(total_books))
                self.stat_cards['available_books'].configure(text=str(available_books))
                self.stat_cards['borrowed_books'].configure(text=str(borrowed_books))
                self.stat_cards['overdue_books'].configure(text=str(overdue_books))
                self.stat_cards['total_members'].configure(text=str(total_members))
                self.stat_cards['active_members'].configure(text=str(active_members))
                self.stat_cards['new_this_month'].configure(text=str(new_this_month))

            # Update sidebar stats
            if hasattr(self, 'stat_items'):
                self.stat_items['total_books'].configure(text=str(total_books))
                self.stat_items['available_books'].configure(text=str(available_books))
                self.stat_items['borrowed_books'].configure(text=str(borrowed_books))
                self.stat_items['overdue_books'].configure(text=str(overdue_books))
                self.stat_items['total_members'].configure(text=str(total_members))

            # Update activity
            if hasattr(self, 'activity_text'):
                activity = self.get_recent_activity()
                self.activity_text.delete("0.0", "end")
                self.activity_text.insert("0.0", activity)

        except Exception as e:
            print(f"Error refreshing data: {e}")

    def get_recent_activity(self):
        """Get recent activity text"""
        try:
            # Get recent activity from database
            query = '''
                SELECT action, entity_type, entity_id, details, timestamp
                FROM activity_log
                ORDER BY timestamp DESC
                LIMIT 10
            '''
            activities = self.db.execute_query(query, fetch=True)

            if not activities:
                return f"{self.t.get('no_recent_activity', 'No recent activity')}"

            activity_text = f"{self.t.get('recent_activity', 'Recent Activity')}:\n\n"
            for activity in activities:
                timestamp = datetime.fromisoformat(activity[4]).strftime('%H:%M')
                activity_text += f"• {timestamp} - {activity[0]} {activity[1]}: {activity[3] or activity[2]}\n"

            return activity_text

        except Exception as e:
            return f"{self.t.get('error_loading_activity', 'Error loading activity')}: {str(e)}"

    # Event handlers for circulation page
    def on_member_id_change(self, event=None):
        """Handle member ID entry change"""
        member_id = self.member_id_entry.get().strip()
        if member_id:
            try:
                # Find member in database
                query = "SELECT * FROM members WHERE member_id = ?"
                member = self.db.execute_query(query, (member_id,), fetch=True)

                if member:
                    member = member[0]
                    info_text = f"{self.t.get('name', 'Name')}: {member[2]}\n"
                    info_text += f"{self.t.get('class_grade', 'Class')}: {member[3] or self.t.get('not_specified', 'Not Specified')}\n"
                    info_text += f"{self.t.get('status', 'Status')}: {member[15]}\n"
                    info_text += f"{self.t.get('phone', 'Phone')}: {member[5] or 'N/A'}"

                    self.member_info_label.configure(text=info_text)

                    # Show current loans
                    self.update_member_loans(member_id)
                else:
                    self.member_info_label.configure(text=self.t.get('member_not_found', 'Member not found'))
                    self.loans_text.delete("0.0", "end")
            except Exception as e:
                self.member_info_label.configure(text=f"{self.t.get('error', 'Error')}: {str(e)}")
        else:
            self.member_info_label.configure(text=self.t.get('no_member_selected', 'No member selected'))
            self.loans_text.delete("0.0", "end")

    def on_book_barcode_change(self, event=None):
        """Handle book barcode entry change"""
        barcode = self.book_barcode_entry.get().strip()
        if barcode:
            try:
                # Find book in database
                query = "SELECT * FROM books WHERE barcode = ?"
                book = self.db.execute_query(query, (barcode,), fetch=True)

                if book:
                    book = book[0]
                    info_text = f"{self.t.get('title', 'Title')}: {book[2]}\n"
                    info_text += f"{self.t.get('author', 'Author')}: {book[3]}\n"
                    info_text += f"{self.t.get('status', 'Status')}: {book[14]}\n"
                    info_text += f"{self.t.get('location', 'Location')}: {book[15] or 'N/A'}"

                    self.book_info_label.configure(text=info_text)
                else:
                    self.book_info_label.configure(text=self.t.get('book_not_found', 'Book not found'))
            except Exception as e:
                self.book_info_label.configure(text=f"{self.t.get('error', 'Error')}: {str(e)}")
        else:
            self.book_info_label.configure(text=self.t.get('no_book_selected', 'No book selected'))

    def update_member_loans(self, member_id):
        """Update member loans display"""
        try:
            query = '''
                SELECT b.title, b.author, t.transaction_date, t.due_date, t.fine_amount
                FROM transactions t
                JOIN books b ON t.book_barcode = b.barcode
                WHERE t.member_id = ? AND t.transaction_type = 'borrow' AND t.return_date IS NULL
                ORDER BY t.transaction_date DESC
            '''
            loans = self.db.execute_query(query, (member_id,), fetch=True)

            if loans:
                loans_text = f"{self.t.get('current_loans', 'Current Loans')}:\n\n"
                for loan in loans:
                    loans_text += f"📚 {loan[0]}\n"
                    loans_text += f"   {self.t.get('author', 'Author')}: {loan[1]}\n"
                    loans_text += f"   {self.t.get('borrowed', 'Borrowed')}: {loan[2]}\n"
                    loans_text += f"   {self.t.get('due_date', 'Due')}: {loan[3]}\n"
                    if loan[4] > 0:
                        loans_text += f"   {self.t.get('fine', 'Fine')}: {loan[4]} {self.config.get('library_settings.currency', 'IQD')}\n"
                    loans_text += "\n"
            else:
                loans_text = self.t.get('no_current_loans', 'No current loans')

            self.loans_text.delete("0.0", "end")
            self.loans_text.insert("0.0", loans_text)

        except Exception as e:
            self.loans_text.delete("0.0", "end")
            self.loans_text.insert("0.0", f"{self.t.get('error', 'Error')}: {str(e)}")

    def borrow_book(self):
        """Handle book borrowing"""
        member_id = self.member_id_entry.get().strip()
        barcode = self.book_barcode_entry.get().strip()

        if not member_id or not barcode:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('enter_member_and_book', 'Please enter both member ID and book barcode')
            )
            return

        try:
            # Check if member exists and is active
            member_query = "SELECT * FROM members WHERE member_id = ? AND status = 'Active'"
            member = self.db.execute_query(member_query, (member_id,), fetch=True)

            if not member:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('member_not_found_or_inactive', 'Member not found or inactive')
                )
                return

            # Check if book exists and is available
            book_query = "SELECT * FROM books WHERE barcode = ? AND status = 'Available'"
            book = self.db.execute_query(book_query, (barcode,), fetch=True)

            if not book:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('book_not_available', 'Book not found or not available')
                )
                return

            # Check loan limits
            max_books = self.config.get('library_settings.max_books_per_student', 3)
            current_loans_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE member_id = ? AND transaction_type = 'borrow' AND return_date IS NULL
            '''
            current_loans = self.db.execute_query(current_loans_query, (member_id,), fetch=True)[0][0]

            if current_loans >= max_books:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('loan_limit_exceeded', 'Loan limit exceeded')} ({max_books})"
                )
                return

            # Calculate due date
            loan_period = self.config.get('library_settings.loan_period_days', 14)
            due_date = (datetime.now() + timedelta(days=loan_period)).date()

            # Create transaction
            transaction_query = '''
                INSERT INTO transactions (book_barcode, member_id, transaction_type,
                                        transaction_date, due_date)
                VALUES (?, ?, 'borrow', ?, ?)
            '''
            self.db.execute_query(transaction_query, (barcode, member_id, datetime.now().date(), due_date))

            # Update book status
            self.db.execute_query("UPDATE books SET status = 'Borrowed' WHERE barcode = ?", (barcode,))

            # Log activity
            self.db.log_activity('BORROW', 'BOOK', barcode, f'Borrowed by {member_id}')

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('book_borrowed_successfully', 'Book borrowed successfully')}\n"
                f"{self.t.get('due_date', 'Due date')}: {due_date}"
            )

            # Clear entries and refresh
            self.member_id_entry.delete(0, "end")
            self.book_barcode_entry.delete(0, "end")
            self.member_info_label.configure(text=self.t.get('no_member_selected', 'No member selected'))
            self.book_info_label.configure(text=self.t.get('no_book_selected', 'No book selected'))
            self.loans_text.delete("0.0", "end")
            self.refresh_data()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('borrow_error', 'Error borrowing book')}: {str(e)}"
            )

    def return_book(self):
        """Handle book return"""
        barcode = self.book_barcode_entry.get().strip()

        if not barcode:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('enter_book_barcode', 'Please enter book barcode')
            )
            return

        try:
            # Find active loan for this book
            loan_query = '''
                SELECT * FROM transactions
                WHERE book_barcode = ? AND transaction_type = 'borrow' AND return_date IS NULL
            '''
            loan = self.db.execute_query(loan_query, (barcode,), fetch=True)

            if not loan:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('no_active_loan', 'No active loan found for this book')
                )
                return

            loan = loan[0]
            due_date = datetime.strptime(loan[6], '%Y-%m-%d').date()
            return_date = datetime.now().date()

            # Calculate fine if overdue
            fine_amount = 0
            if return_date > due_date:
                days_overdue = (return_date - due_date).days
                fine_per_day = self.config.get('library_settings.fine_per_day', 250)
                fine_amount = days_overdue * fine_per_day

            # Update transaction
            update_query = '''
                UPDATE transactions
                SET return_date = ?, fine_amount = ?
                WHERE id = ?
            '''
            self.db.execute_query(update_query, (return_date, fine_amount, loan[0]))

            # Update book status
            self.db.execute_query("UPDATE books SET status = 'Available' WHERE barcode = ?", (barcode,))

            # Log activity
            self.db.log_activity('RETURN', 'BOOK', barcode, f'Returned by {loan[3]}')

            # Show success message
            success_msg = self.t.get('book_returned_successfully', 'Book returned successfully')
            if fine_amount > 0:
                currency = self.config.get('library_settings.currency', 'IQD')
                success_msg += f"\n{self.t.get('fine_amount', 'Fine amount')}: {fine_amount} {currency}"

            messagebox.showinfo(self.t.get('success', 'Success'), success_msg)

            # Clear entries and refresh
            self.book_barcode_entry.delete(0, "end")
            self.book_info_label.configure(text=self.t.get('no_book_selected', 'No book selected'))
            self.refresh_data()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('return_error', 'Error returning book')}: {str(e)}"
            )

    # Books management functionality
    def load_books_display(self):
        """Load and display books"""
        try:
            # Clear existing widgets
            for widget in self.books_widgets:
                widget.destroy()
            self.books_widgets.clear()

            # Get books from database
            books = self.db.get_all_books()

            if not books:
                no_books_label = ctk.CTkLabel(
                    self.books_list_frame,
                    text=self.t.get('no_books_found', 'No books found'),
                    font=ctk.CTkFont(size=16)
                )
                no_books_label.pack(pady=50)
                self.books_widgets.append(no_books_label)
                return

            # Display books in cards
            for book in books:
                self.create_book_card(book)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.books_list_frame,
                text=f"{self.t.get('error_loading_books', 'Error loading books')}: {str(e)}",
                font=ctk.CTkFont(size=14)
            )
            error_label.pack(pady=20)
            self.books_widgets.append(error_label)

    def create_book_card(self, book):
        """Create a book display card"""
        card = ctk.CTkFrame(self.books_list_frame)
        card.pack(fill="x", padx=10, pady=5)
        self.books_widgets.append(card)

        # Book info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Title and author
        title_label = ctk.CTkLabel(
            info_frame,
            text=f"📚 {book[2]}",  # title
            font=ctk.CTkFont(size=16, weight="bold"),
            anchor="w"
        )
        title_label.pack(anchor="w", pady=(0, 5))

        author_label = ctk.CTkLabel(
            info_frame,
            text=f"{self.t.get('author', 'Author')}: {book[3]}",  # author
            font=ctk.CTkFont(size=12),
            anchor="w"
        )
        author_label.pack(anchor="w", pady=(0, 5))

        # Additional info
        details = []
        if book[8]:  # category
            details.append(f"{self.t.get('category', 'Category')}: {book[8]}")
        if book[1]:  # barcode
            details.append(f"{self.t.get('barcode', 'Barcode')}: {book[1]}")
        if book[14]:  # status
            details.append(f"{self.t.get('status', 'Status')}: {book[14]}")

        details_text = " | ".join(details)
        details_label = ctk.CTkLabel(
            info_frame,
            text=details_text,
            font=ctk.CTkFont(size=10),
            text_color="gray",
            anchor="w"
        )
        details_label.pack(anchor="w")

        # Action buttons
        actions_frame = ctk.CTkFrame(card)
        actions_frame.pack(side="right", padx=15, pady=15)

        # Status color
        status_color = COLORS['success'] if book[14] == 'Available' else COLORS['warning']

        ctk.CTkButton(
            actions_frame,
            text=f"✏️ {self.t.get('edit', 'Edit')}",
            command=lambda b=book: self.edit_book_dialog(b),
            width=80,
            height=30,
            fg_color=COLORS['primary'],
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

        ctk.CTkButton(
            actions_frame,
            text=f"🗑️ {self.t.get('delete', 'Delete')}",
            command=lambda b=book: self.delete_book(b),
            width=80,
            height=30,
            fg_color=COLORS['danger'],
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

        # Status indicator
        status_label = ctk.CTkLabel(
            actions_frame,
            text=book[14],
            font=ctk.CTkFont(size=10, weight="bold"),
            fg_color=status_color,
            corner_radius=10,
            width=80,
            height=25
        )
        status_label.pack(pady=2)

    def on_books_search_change(self, *args):
        """Handle books search change"""
        search_term = self.books_search_var.get().lower()
        self.filter_books_display(search_term=search_term)

    def on_books_filter_change(self, *args):
        """Handle books filter change"""
        self.filter_books_display()

    def filter_books_display(self, search_term=None):
        """Filter books display based on search and filters"""
        try:
            if search_term is None:
                search_term = self.books_search_var.get().lower()

            status_filter = self.books_status_filter.get()
            category_filter = self.books_category_filter.get()

            # Clear existing display
            for widget in self.books_widgets:
                widget.destroy()
            self.books_widgets.clear()

            # Get all books
            books = self.db.get_all_books()
            filtered_books = []

            for book in books:
                # Apply search filter
                if search_term:
                    searchable_text = f"{book[2]} {book[3]} {book[1]}".lower()  # title, author, barcode
                    if search_term not in searchable_text:
                        continue

                # Apply status filter
                if status_filter != "all" and book[14] != status_filter:
                    continue

                # Apply category filter
                if category_filter != "all" and book[8] != category_filter:
                    continue

                filtered_books.append(book)

            # Display filtered books
            if filtered_books:
                for book in filtered_books:
                    self.create_book_card(book)
            else:
                no_results_label = ctk.CTkLabel(
                    self.books_list_frame,
                    text=self.t.get('no_books_match_filter', 'No books match the current filter'),
                    font=ctk.CTkFont(size=16)
                )
                no_results_label.pack(pady=50)
                self.books_widgets.append(no_results_label)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.books_list_frame,
                text=f"{self.t.get('error_filtering_books', 'Error filtering books')}: {str(e)}",
                font=ctk.CTkFont(size=14)
            )
            error_label.pack(pady=20)
            self.books_widgets.append(error_label)

    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = AddBookDialog(self, self.config, self.db)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_books_display()
            self.refresh_data()

    def edit_book_dialog(self, book):
        """Show edit book dialog"""
        dialog = EditBookDialog(self, self.config, self.db, book)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_books_display()
            self.refresh_data()

    def delete_book(self, book):
        """Delete a book"""
        confirm_msg = f"{self.t.get('confirm_delete_book', 'Are you sure you want to delete this book?')}\n\n"
        confirm_msg += f"{self.t.get('title', 'Title')}: {book[2]}\n"
        confirm_msg += f"{self.t.get('author', 'Author')}: {book[3]}"

        if messagebox.askyesno(self.t.get('confirm_delete', 'Confirm Delete'), confirm_msg):
            try:
                # Check if book is currently borrowed
                loan_query = '''
                    SELECT COUNT(*) FROM transactions
                    WHERE book_barcode = ? AND transaction_type = 'borrow' AND return_date IS NULL
                '''
                active_loans = self.db.execute_query(loan_query, (book[1],), fetch=True)[0][0]

                if active_loans > 0:
                    messagebox.showerror(
                        self.t.get('error', 'Error'),
                        self.t.get('cannot_delete_borrowed_book', 'Cannot delete book that is currently borrowed')
                    )
                    return

                # Delete book
                self.db.execute_query("DELETE FROM books WHERE id = ?", (book[0],))

                # Log activity
                self.db.log_activity('DELETE', 'BOOK', book[1], f'Deleted book: {book[2]}')

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    self.t.get('book_deleted_successfully', 'Book deleted successfully')
                )

                self.load_books_display()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('delete_error', 'Error deleting book')}: {str(e)}"
                )

    def import_books_csv(self):
        """Import books from CSV file"""
        file_path = filedialog.askopenfilename(
            title=self.t.get('select_csv_file', 'Select CSV File'),
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                df = pd.read_csv(file_path)
                imported_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Prepare book data
                        book_data = (
                            row.get('barcode', f"BOOK{index+1:06d}"),
                            row.get('title', ''),
                            row.get('author', ''),
                            row.get('ddc_number', ''),
                            row.get('author_code', ''),
                            row.get('call_number', ''),
                            row.get('isbn', ''),
                            row.get('category', ''),
                            row.get('publisher', ''),
                            row.get('publication_year', None),
                            row.get('pages', None),
                            row.get('language', 'کوردی'),
                            row.get('summary', ''),
                            row.get('location', ''),
                            row.get('cover_image', ''),
                            row.get('price', None),
                            row.get('condition_status', 'Good'),
                            row.get('notes', '')
                        )

                        self.db.add_book(book_data)
                        imported_count += 1

                    except Exception as e:
                        errors.append(f"Row {index+1}: {str(e)}")

                # Show results
                result_msg = f"{self.t.get('import_completed', 'Import completed')}\n"
                result_msg += f"{self.t.get('imported_books', 'Imported books')}: {imported_count}\n"

                if errors:
                    result_msg += f"{self.t.get('errors', 'Errors')}: {len(errors)}\n\n"
                    result_msg += "\n".join(errors[:5])  # Show first 5 errors
                    if len(errors) > 5:
                        result_msg += f"\n... and {len(errors) - 5} more errors"

                messagebox.showinfo(self.t.get('import_results', 'Import Results'), result_msg)

                if imported_count > 0:
                    self.load_books_display()
                    self.refresh_data()

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('import_error', 'Import error')}: {str(e)}"
                )

    def export_books_advanced(self):
        """Show advanced export dialog for books"""
        dialog = AdvancedExportDialog(self, "books", self.config)
        self.wait_window(dialog)

        if hasattr(dialog, 'result') and dialog.result:
            try:
                options = dialog.result

                # Get books data
                books = self.db.get_all_books()

                # Apply date filter if specified
                if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
                    # Filter books by date_added
                    filtered_books = []
                    for book in books:
                        if book[21]:  # date_added
                            book_date = datetime.strptime(book[21], '%Y-%m-%d').date()
                            if options['from_date'] <= book_date <= options['to_date']:
                                filtered_books.append(book)
                    books = filtered_books

                # Generate sample data if requested
                if options.get('generate_sample'):
                    sample_count = int(options.get('sample_count', 50))
                    export_lang = options.get('export_language', 'current')

                    if export_lang == 'all':
                        sample_books = self.sample_generator.generate_sample_books(sample_count, 'all')
                    else:
                        sample_books = self.sample_generator.generate_sample_books(sample_count, self.current_lang)

                    # Convert to DataFrame
                    df = pd.DataFrame(sample_books)
                else:
                    # Convert books to DataFrame
                    columns = ['id', 'barcode', 'title', 'author', 'ddc_number', 'author_code',
                              'call_number', 'isbn', 'category', 'publisher', 'publication_year',
                              'pages', 'language', 'summary', 'status', 'location', 'cover_image',
                              'acquisition_date', 'price', 'condition_status', 'notes',
                              'date_added', 'created_at', 'updated_at']

                    books_data = []
                    for book in books:
                        book_dict = dict(zip(columns, book))
                        books_data.append(book_dict)

                    df = pd.DataFrame(books_data)

                # Export based on format
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                exports_folder = self.config.get('folders.exports', 'exports')

                if options['format'] == 'excel':
                    filename = f"books_export_{timestamp}.xlsx"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_excel(filepath, index=False)
                elif options['format'] == 'csv':
                    filename = f"books_export_{timestamp}.csv"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_csv(filepath, index=False, encoding='utf-8-sig')
                elif options['format'] == 'json':
                    filename = f"books_export_{timestamp}.json"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_json(filepath, orient='records', force_ascii=False, indent=2)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('export_completed', 'Export completed')}\n{filepath}"
                )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('export_error', 'Export error')}: {str(e)}"
                )

    # Members management functionality
    def load_members_display(self):
        """Load and display members"""
        try:
            # Clear existing widgets
            for widget in self.members_widgets:
                widget.destroy()
            self.members_widgets.clear()

            # Get members from database
            members = self.db.get_all_members()

            if not members:
                no_members_label = ctk.CTkLabel(
                    self.members_list_frame,
                    text=self.t.get('no_members_found', 'No members found'),
                    font=ctk.CTkFont(size=16)
                )
                no_members_label.pack(pady=50)
                self.members_widgets.append(no_members_label)
                return

            # Display members in cards
            for member in members:
                self.create_member_card(member)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.members_list_frame,
                text=f"{self.t.get('error_loading_members', 'Error loading members')}: {str(e)}",
                font=ctk.CTkFont(size=14)
            )
            error_label.pack(pady=20)
            self.members_widgets.append(error_label)

    def create_member_card(self, member):
        """Create a member display card"""
        card = ctk.CTkFrame(self.members_list_frame)
        card.pack(fill="x", padx=10, pady=5)
        self.members_widgets.append(card)

        # Member info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Name and ID
        name_label = ctk.CTkLabel(
            info_frame,
            text=f"👤 {member[2]}",  # name
            font=ctk.CTkFont(size=16, weight="bold"),
            anchor="w"
        )
        name_label.pack(anchor="w", pady=(0, 5))

        id_label = ctk.CTkLabel(
            info_frame,
            text=f"{self.t.get('member_id', 'Member ID')}: {member[1]}",  # member_id
            font=ctk.CTkFont(size=12),
            anchor="w"
        )
        id_label.pack(anchor="w", pady=(0, 5))

        # Additional info
        details = []
        if member[3]:  # class_grade
            details.append(f"{self.t.get('class_grade', 'Class')}: {member[3]}")
        if member[5]:  # phone
            details.append(f"{self.t.get('phone', 'Phone')}: {member[5]}")
        if member[15]:  # status
            details.append(f"{self.t.get('status', 'Status')}: {member[15]}")

        details_text = " | ".join(details)
        details_label = ctk.CTkLabel(
            info_frame,
            text=details_text,
            font=ctk.CTkFont(size=10),
            text_color="gray",
            anchor="w"
        )
        details_label.pack(anchor="w")

        # Action buttons
        actions_frame = ctk.CTkFrame(card)
        actions_frame.pack(side="right", padx=15, pady=15)

        # Status color
        status_color = COLORS['success'] if member[15] == 'Active' else COLORS['warning']

        ctk.CTkButton(
            actions_frame,
            text=f"✏️ {self.t.get('edit', 'Edit')}",
            command=lambda m=member: self.edit_member_dialog(m),
            width=80,
            height=30,
            fg_color=COLORS['primary'],
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

        ctk.CTkButton(
            actions_frame,
            text=f"🆔 {self.t.get('id_card', 'ID Card')}",
            command=lambda m=member: self.generate_single_member_card(m),
            width=80,
            height=30,
            fg_color=COLORS['accent'],
            font=ctk.CTkFont(size=11)
        ).pack(pady=2)

        # Status indicator
        status_label = ctk.CTkLabel(
            actions_frame,
            text=member[15],
            font=ctk.CTkFont(size=10, weight="bold"),
            fg_color=status_color,
            corner_radius=10,
            width=80,
            height=25
        )
        status_label.pack(pady=2)

    def on_members_search_change(self, *args):
        """Handle members search change"""
        search_term = self.members_search_var.get().lower()
        self.filter_members_display(search_term=search_term)

    def on_members_filter_change(self, *args):
        """Handle members filter change"""
        self.filter_members_display()

    def filter_members_display(self, search_term=None):
        """Filter members display based on search and filters"""
        try:
            if search_term is None:
                search_term = self.members_search_var.get().lower()

            status_filter = self.members_status_filter.get()
            type_filter = self.members_type_filter.get()

            # Clear existing display
            for widget in self.members_widgets:
                widget.destroy()
            self.members_widgets.clear()

            # Get all members
            members = self.db.get_all_members()
            filtered_members = []

            for member in members:
                # Apply search filter
                if search_term:
                    searchable_text = f"{member[2]} {member[1]} {member[3] or ''}".lower()  # name, member_id, class
                    if search_term not in searchable_text:
                        continue

                # Apply status filter
                if status_filter != "all" and member[15] != status_filter:
                    continue

                # Apply type filter
                if type_filter != "all" and member[16] != type_filter:
                    continue

                filtered_members.append(member)

            # Display filtered members
            if filtered_members:
                for member in filtered_members:
                    self.create_member_card(member)
            else:
                no_results_label = ctk.CTkLabel(
                    self.members_list_frame,
                    text=self.t.get('no_members_match_filter', 'No members match the current filter'),
                    font=ctk.CTkFont(size=16)
                )
                no_results_label.pack(pady=50)
                self.members_widgets.append(no_results_label)

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.members_list_frame,
                text=f"{self.t.get('error_filtering_members', 'Error filtering members')}: {str(e)}",
                font=ctk.CTkFont(size=14)
            )
            error_label.pack(pady=20)
            self.members_widgets.append(error_label)

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = AddMemberDialog(self, self.config, self.db)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_members_display()
            self.refresh_data()

    def edit_member_dialog(self, member):
        """Show edit member dialog"""
        dialog = EditMemberDialog(self, self.config, self.db, member)
        self.wait_window(dialog)
        if hasattr(dialog, 'result') and dialog.result:
            self.load_members_display()
            self.refresh_data()

    def generate_single_member_card(self, member):
        """Generate ID card for a single member"""
        try:
            card_path = self.id_generator.generate_member_id_card(member[1])

            # Create HTML page with single card
            html_path = self.id_generator.create_beautiful_html_cards([card_path])

            # Open in browser
            webbrowser.open(f'file://{os.path.abspath(html_path)}')

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('id_card_generated', 'ID card generated successfully')}\n{card_path}"
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('id_card_error', 'Error generating ID card')}: {str(e)}"
            )

    def generate_member_cards(self):
        """Generate ID cards for all active members"""
        try:
            # Get all active members
            query = "SELECT member_id FROM members WHERE status = 'Active'"
            active_members = self.db.execute_query(query, fetch=True)

            if not active_members:
                messagebox.showwarning(
                    self.t.get('warning', 'Warning'),
                    self.t.get('no_active_members', 'No active members found')
                )
                return

            # Generate cards for all members
            card_paths = []
            for member_row in active_members:
                member_id = member_row[0]
                try:
                    card_path = self.id_generator.generate_member_id_card(member_id)
                    card_paths.append(card_path)
                except Exception as e:
                    print(f"Error generating card for {member_id}: {e}")

            if card_paths:
                # Create beautiful HTML page
                html_path = self.id_generator.create_beautiful_html_cards(card_paths)

                # Open in browser
                webbrowser.open(f'file://{os.path.abspath(html_path)}')

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('id_cards_generated', 'ID cards generated successfully')}\n"
                    f"{self.t.get('total_cards', 'Total cards')}: {len(card_paths)}\n"
                    f"{self.t.get('html_file', 'HTML file')}: {html_path}"
                )
            else:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('no_cards_generated', 'No cards could be generated')
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('cards_generation_error', 'Error generating cards')}: {str(e)}"
            )

    def export_members_advanced(self):
        """Show advanced export dialog for members"""
        dialog = AdvancedExportDialog(self, "members", self.config)
        self.wait_window(dialog)

        if hasattr(dialog, 'result') and dialog.result:
            try:
                options = dialog.result

                # Get members data
                members = self.db.get_all_members()

                # Apply date filter if specified
                if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
                    # Filter members by registration_date
                    filtered_members = []
                    for member in members:
                        if member[13]:  # registration_date
                            member_date = datetime.strptime(member[13], '%Y-%m-%d').date()
                            if options['from_date'] <= member_date <= options['to_date']:
                                filtered_members.append(member)
                    members = filtered_members

                # Generate sample data if requested
                if options.get('generate_sample'):
                    sample_count = int(options.get('sample_count', 30))
                    export_lang = options.get('export_language', 'current')

                    if export_lang == 'all':
                        sample_members = self.sample_generator.generate_sample_members(sample_count, 'all')
                    else:
                        sample_members = self.sample_generator.generate_sample_members(sample_count, self.current_lang)

                    # Convert to DataFrame
                    df = pd.DataFrame(sample_members)
                else:
                    # Convert members to DataFrame
                    columns = ['id', 'member_id', 'name', 'class_grade', 'national_id', 'phone',
                              'guardian_name', 'guardian_phone', 'address', 'email', 'date_of_birth',
                              'gender', 'photo_path', 'registration_date', 'expiry_date', 'status',
                              'membership_type', 'emergency_contact', 'emergency_phone', 'notes',
                              'created_at', 'updated_at']

                    members_data = []
                    for member in members:
                        member_dict = dict(zip(columns, member))
                        members_data.append(member_dict)

                    df = pd.DataFrame(members_data)

                # Export based on format
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                exports_folder = self.config.get('folders.exports', 'exports')

                if options['format'] == 'excel':
                    filename = f"members_export_{timestamp}.xlsx"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_excel(filepath, index=False)
                elif options['format'] == 'csv':
                    filename = f"members_export_{timestamp}.csv"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_csv(filepath, index=False, encoding='utf-8-sig')
                elif options['format'] == 'json':
                    filename = f"members_export_{timestamp}.json"
                    filepath = os.path.join(exports_folder, filename)
                    df.to_json(filepath, orient='records', force_ascii=False, indent=2)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('export_completed', 'Export completed')}\n{filepath}"
                )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('export_error', 'Export error')}: {str(e)}"
                )

# ==============================================================================
# Main Execution
# ==============================================================================

if __name__ == "__main__":
    try:
        # Create and run the enhanced application
        app = EnhancedModernLibraryApp()
        app.mainloop()
    except Exception as e:
        import traceback
        print(f"Error starting application: {e}")
        print("Full traceback:")
        traceback.print_exc()

        # Try to show error in appropriate language
        try:
            config = ConfigManager()
            current_lang = config.get('language', 'kurdish')
            t = TRANSLATIONS.get(current_lang, TRANSLATIONS['kurdish'])
            error_title = t.get('error', 'Error')
            error_msg = f"{t.get('startup_error', 'Error starting application')}: {str(e)}"
        except:
            error_title = "Error"
            error_msg = f"Error starting application: {str(e)}"

        messagebox.showerror(error_title, error_msg)

# ==============================================================================
# Dialog Classes for Adding/Editing
# ==============================================================================

class AddBookDialog(ctk.CTkToplevel):
    def __init__(self, parent, config_manager, db_manager):
        super().__init__(parent)

        self.parent = parent
        self.config = config_manager
        self.db = db_manager
        self.current_lang = self.config.get('language', 'kurdish')
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])
        self.result = False

        self.title(self.t.get('add_book', 'Add Book'))
        self.geometry("600x700")
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.center_window()

    def center_window(self):
        """Center the dialog on parent"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

    def create_widgets(self):
        """Create dialog widgets"""
        # Main scrollable frame
        main_frame = ctk.CTkScrollableFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=f"📚 {self.t.get('add_new_book', 'Add New Book')}",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Form fields
        self.entries = {}

        # Required fields
        required_fields = [
            ('barcode', self.t.get('barcode', 'Barcode'), self.t.get('enter_barcode', 'Enter barcode')),
            ('title', self.t.get('title', 'Title'), self.t.get('enter_title', 'Enter title')),
            ('author', self.t.get('author', 'Author'), self.t.get('enter_author', 'Enter author name'))
        ]

        for field_key, field_label, placeholder in required_fields:
            self.create_form_field(main_frame, field_key, field_label, placeholder, required=True)

        # Optional fields
        optional_fields = [
            ('ddc_number', self.t.get('ddc_number', 'DDC Number'), '000.000'),
            ('author_code', self.t.get('author_code', 'Author Code'), 'ABC'),
            ('call_number', self.t.get('call_number', 'Call Number'), '000.000 ABC'),
            ('isbn', self.t.get('isbn', 'ISBN'), '978-0000000000'),
            ('category', self.t.get('category', 'Category'), self.t.get('enter_category', 'Enter category')),
            ('publisher', self.t.get('publisher', 'Publisher'), self.t.get('enter_publisher', 'Enter publisher')),
            ('publication_year', self.t.get('publication_year', 'Publication Year'), '2024'),
            ('pages', self.t.get('pages', 'Pages'), '100'),
            ('language', self.t.get('language', 'Language'), 'کوردی'),
            ('location', self.t.get('location', 'Location'), 'A-1-01'),
            ('price', self.t.get('price', 'Price'), '25000'),
            ('condition_status', self.t.get('condition_status', 'Condition'), 'Good')
        ]

        for field_key, field_label, placeholder in optional_fields:
            self.create_form_field(main_frame, field_key, field_label, placeholder)

        # Summary field (text area)
        summary_frame = ctk.CTkFrame(main_frame)
        summary_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(summary_frame, text=f"{self.t.get('summary', 'Summary')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.entries['summary'] = ctk.CTkTextbox(summary_frame, height=80)
        self.entries['summary'].pack(fill="x", padx=15, pady=(0, 15))

        # Notes field (text area)
        notes_frame = ctk.CTkFrame(main_frame)
        notes_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(notes_frame, text=f"{self.t.get('notes', 'Notes')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.entries['notes'] = ctk.CTkTextbox(notes_frame, height=60)
        self.entries['notes'].pack(fill="x", padx=15, pady=(0, 15))

        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=(20, 0))

        ctk.CTkButton(
            buttons_frame,
            text=f"💾 {self.t.get('save', 'Save')}",
            command=self.save_book,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['success']
        ).pack(side="left", padx=(15, 10), pady=15)

        ctk.CTkButton(
            buttons_frame,
            text=f"❌ {self.t.get('cancel', 'Cancel')}",
            command=self.destroy,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['danger']
        ).pack(side="right", padx=(10, 15), pady=15)

    def create_form_field(self, parent, field_key, field_label, placeholder, required=False):
        """Create a form field"""
        field_frame = ctk.CTkFrame(parent)
        field_frame.pack(fill="x", pady=5)

        label_text = f"{field_label}{'*' if required else ''}:"
        ctk.CTkLabel(field_frame, text=label_text,
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        entry = ctk.CTkEntry(field_frame, placeholder_text=placeholder, font=ctk.CTkFont(size=12))
        entry.pack(fill="x", padx=15, pady=(0, 15))

        self.entries[field_key] = entry

    def save_book(self):
        """Save the book"""
        try:
            # Validate required fields
            required_fields = ['barcode', 'title', 'author']
            for field in required_fields:
                if not self.entries[field].get().strip():
                    messagebox.showerror(
                        self.t.get('error', 'Error'),
                        f"{self.t.get('field_required', 'Field required')}: {field}"
                    )
                    return

            # Check if barcode already exists
            existing_book = self.db.execute_query(
                "SELECT id FROM books WHERE barcode = ?",
                (self.entries['barcode'].get().strip(),),
                fetch=True
            )

            if existing_book:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('barcode_already_exists', 'Barcode already exists')
                )
                return

            # Prepare book data
            book_data = (
                self.entries['barcode'].get().strip(),
                self.entries['title'].get().strip(),
                self.entries['author'].get().strip(),
                self.entries['ddc_number'].get().strip() or None,
                self.entries['author_code'].get().strip() or None,
                self.entries['call_number'].get().strip() or None,
                self.entries['isbn'].get().strip() or None,
                self.entries['category'].get().strip() or None,
                self.entries['publisher'].get().strip() or None,
                int(self.entries['publication_year'].get().strip()) if self.entries['publication_year'].get().strip().isdigit() else None,
                int(self.entries['pages'].get().strip()) if self.entries['pages'].get().strip().isdigit() else None,
                self.entries['language'].get().strip() or 'کوردی',
                self.entries['summary'].get("0.0", "end").strip() or None,
                self.entries['location'].get().strip() or None,
                None,  # cover_image
                float(self.entries['price'].get().strip()) if self.entries['price'].get().strip().replace('.', '').isdigit() else None,
                self.entries['condition_status'].get().strip() or 'Good',
                self.entries['notes'].get("0.0", "end").strip() or None
            )

            # Add book to database
            self.db.add_book(book_data)

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('book_added_successfully', 'Book added successfully')
            )

            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_error', 'Error saving book')}: {str(e)}"
            )

class EditBookDialog(AddBookDialog):
    def __init__(self, parent, config_manager, db_manager, book):
        self.book = book
        super().__init__(parent, config_manager, db_manager)
        self.title(self.t.get('edit_book', 'Edit Book'))
        self.populate_fields()

    def populate_fields(self):
        """Populate fields with existing book data"""
        try:
            # Map database columns to form fields
            field_mapping = {
                'barcode': self.book[1],
                'title': self.book[2],
                'author': self.book[3],
                'ddc_number': self.book[4] or '',
                'author_code': self.book[5] or '',
                'call_number': self.book[6] or '',
                'isbn': self.book[7] or '',
                'category': self.book[8] or '',
                'publisher': self.book[9] or '',
                'publication_year': str(self.book[10]) if self.book[10] else '',
                'pages': str(self.book[11]) if self.book[11] else '',
                'language': self.book[12] or '',
                'location': self.book[15] or '',
                'price': str(self.book[18]) if self.book[18] else '',
                'condition_status': self.book[19] or ''
            }

            # Populate entry fields
            for field_key, value in field_mapping.items():
                if field_key in self.entries:
                    self.entries[field_key].delete(0, "end")
                    self.entries[field_key].insert(0, value)

            # Populate text areas
            if self.book[13]:  # summary
                self.entries['summary'].delete("0.0", "end")
                self.entries['summary'].insert("0.0", self.book[13])

            if self.book[20]:  # notes
                self.entries['notes'].delete("0.0", "end")
                self.entries['notes'].insert("0.0", self.book[20])

        except Exception as e:
            print(f"Error populating fields: {e}")

    def save_book(self):
        """Update the book"""
        try:
            # Validate required fields
            required_fields = ['barcode', 'title', 'author']
            for field in required_fields:
                if not self.entries[field].get().strip():
                    messagebox.showerror(
                        self.t.get('error', 'Error'),
                        f"{self.t.get('field_required', 'Field required')}: {field}"
                    )
                    return

            # Check if barcode already exists (excluding current book)
            existing_book = self.db.execute_query(
                "SELECT id FROM books WHERE barcode = ? AND id != ?",
                (self.entries['barcode'].get().strip(), self.book[0]),
                fetch=True
            )

            if existing_book:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('barcode_already_exists', 'Barcode already exists')
                )
                return

            # Update book in database
            update_query = '''
                UPDATE books SET
                    barcode = ?, title = ?, author = ?, ddc_number = ?, author_code = ?,
                    call_number = ?, isbn = ?, category = ?, publisher = ?, publication_year = ?,
                    pages = ?, language = ?, summary = ?, location = ?, price = ?,
                    condition_status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''

            book_data = (
                self.entries['barcode'].get().strip(),
                self.entries['title'].get().strip(),
                self.entries['author'].get().strip(),
                self.entries['ddc_number'].get().strip() or None,
                self.entries['author_code'].get().strip() or None,
                self.entries['call_number'].get().strip() or None,
                self.entries['isbn'].get().strip() or None,
                self.entries['category'].get().strip() or None,
                self.entries['publisher'].get().strip() or None,
                int(self.entries['publication_year'].get().strip()) if self.entries['publication_year'].get().strip().isdigit() else None,
                int(self.entries['pages'].get().strip()) if self.entries['pages'].get().strip().isdigit() else None,
                self.entries['language'].get().strip() or 'کوردی',
                self.entries['summary'].get("0.0", "end").strip() or None,
                self.entries['location'].get().strip() or None,
                float(self.entries['price'].get().strip()) if self.entries['price'].get().strip().replace('.', '').isdigit() else None,
                self.entries['condition_status'].get().strip() or 'Good',
                self.entries['notes'].get("0.0", "end").strip() or None,
                self.book[0]  # book ID
            )

            self.db.execute_query(update_query, book_data)

            # Log activity
            self.db.log_activity('UPDATE', 'BOOK', self.entries['barcode'].get().strip(),
                               f'Updated book: {self.entries["title"].get().strip()}')

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('book_updated_successfully', 'Book updated successfully')
            )

            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('update_error', 'Error updating book')}: {str(e)}"
            )

class AddMemberDialog(ctk.CTkToplevel):
    def __init__(self, parent, config_manager, db_manager):
        super().__init__(parent)

        self.parent = parent
        self.config = config_manager
        self.db = db_manager
        self.current_lang = self.config.get('language', 'kurdish')
        self.t = TRANSLATIONS.get(self.current_lang, TRANSLATIONS['kurdish'])
        self.result = False

        self.title(self.t.get('add_member', 'Add Member'))
        self.geometry("600x800")
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.center_window()

    def center_window(self):
        """Center the dialog on parent"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (self.winfo_width() // 2)
        y = (self.winfo_screenheight() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")

    def create_widgets(self):
        """Create dialog widgets"""
        # Main scrollable frame
        main_frame = ctk.CTkScrollableFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=f"👤 {self.t.get('add_new_member', 'Add New Member')}",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(0, 20))

        # Form fields
        self.entries = {}

        # Required fields
        required_fields = [
            ('member_id', self.t.get('member_id', 'Member ID'), self.t.get('enter_member_id', 'Enter member ID')),
            ('name', self.t.get('name', 'Name'), self.t.get('enter_name', 'Enter full name'))
        ]

        for field_key, field_label, placeholder in required_fields:
            self.create_form_field(main_frame, field_key, field_label, placeholder, required=True)

        # Optional fields
        optional_fields = [
            ('class_grade', self.t.get('class_grade', 'Class/Grade'), 'Grade 9-A'),
            ('national_id', self.t.get('national_id', 'National ID'), '1234567890'),
            ('phone', self.t.get('phone', 'Phone'), '+964 ************'),
            ('guardian_name', self.t.get('guardian_name', 'Guardian Name'), 'Guardian full name'),
            ('guardian_phone', self.t.get('guardian_phone', 'Guardian Phone'), '+964 ************'),
            ('address', self.t.get('address', 'Address'), 'Full address'),
            ('email', self.t.get('email', 'Email'), '<EMAIL>'),
            ('date_of_birth', self.t.get('date_of_birth', 'Date of Birth'), '2005-01-01'),
            ('emergency_contact', self.t.get('emergency_contact', 'Emergency Contact'), 'Emergency contact name'),
            ('emergency_phone', self.t.get('emergency_phone', 'Emergency Phone'), '+964 ************')
        ]

        for field_key, field_label, placeholder in optional_fields:
            self.create_form_field(main_frame, field_key, field_label, placeholder)

        # Gender selection
        gender_frame = ctk.CTkFrame(main_frame)
        gender_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(gender_frame, text=f"{self.t.get('gender', 'Gender')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.gender_var = ctk.StringVar(value="Male")
        gender_radio_frame = ctk.CTkFrame(gender_frame)
        gender_radio_frame.pack(fill="x", padx=15, pady=(0, 15))

        ctk.CTkRadioButton(gender_radio_frame, text=self.t.get('male', 'Male'),
                          variable=self.gender_var, value="Male").pack(side="left", padx=(0, 20))
        ctk.CTkRadioButton(gender_radio_frame, text=self.t.get('female', 'Female'),
                          variable=self.gender_var, value="Female").pack(side="left")

        # Membership type
        type_frame = ctk.CTkFrame(main_frame)
        type_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(type_frame, text=f"{self.t.get('membership_type', 'Membership Type')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.membership_type_var = ctk.StringVar(value="Student")
        type_menu = ctk.CTkOptionMenu(
            type_frame,
            variable=self.membership_type_var,
            values=["Student", "Teacher", "Staff"],
            width=200
        )
        type_menu.pack(anchor="w", padx=15, pady=(0, 15))

        # Expiry date
        expiry_frame = ctk.CTkFrame(main_frame)
        expiry_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(expiry_frame, text=f"{self.t.get('expiry_date', 'Expiry Date')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        # Default expiry date (1 year from now)
        default_expiry = (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d')
        self.entries['expiry_date'] = ctk.CTkEntry(expiry_frame, placeholder_text=default_expiry,
                                                  font=ctk.CTkFont(size=12))
        self.entries['expiry_date'].pack(anchor="w", padx=15, pady=(0, 15), fill="x")
        self.entries['expiry_date'].insert(0, default_expiry)

        # Notes field
        notes_frame = ctk.CTkFrame(main_frame)
        notes_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(notes_frame, text=f"{self.t.get('notes', 'Notes')}:",
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        self.entries['notes'] = ctk.CTkTextbox(notes_frame, height=60)
        self.entries['notes'].pack(fill="x", padx=15, pady=(0, 15))

        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x", pady=(20, 0))

        ctk.CTkButton(
            buttons_frame,
            text=f"💾 {self.t.get('save', 'Save')}",
            command=self.save_member,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['success']
        ).pack(side="left", padx=(15, 10), pady=15)

        ctk.CTkButton(
            buttons_frame,
            text=f"❌ {self.t.get('cancel', 'Cancel')}",
            command=self.destroy,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=COLORS['danger']
        ).pack(side="right", padx=(10, 15), pady=15)

    def create_form_field(self, parent, field_key, field_label, placeholder, required=False):
        """Create a form field"""
        field_frame = ctk.CTkFrame(parent)
        field_frame.pack(fill="x", pady=5)

        label_text = f"{field_label}{'*' if required else ''}:"
        ctk.CTkLabel(field_frame, text=label_text,
                    font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        entry = ctk.CTkEntry(field_frame, placeholder_text=placeholder, font=ctk.CTkFont(size=12))
        entry.pack(fill="x", padx=15, pady=(0, 15))

        self.entries[field_key] = entry

    def save_member(self):
        """Save the member"""
        try:
            # Validate required fields
            required_fields = ['member_id', 'name']
            for field in required_fields:
                if not self.entries[field].get().strip():
                    messagebox.showerror(
                        self.t.get('error', 'Error'),
                        f"{self.t.get('field_required', 'Field required')}: {field}"
                    )
                    return

            # Check if member ID already exists
            existing_member = self.db.execute_query(
                "SELECT id FROM members WHERE member_id = ?",
                (self.entries['member_id'].get().strip(),),
                fetch=True
            )

            if existing_member:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('member_id_already_exists', 'Member ID already exists')
                )
                return

            # Prepare member data
            member_data = (
                self.entries['member_id'].get().strip(),
                self.entries['name'].get().strip(),
                self.entries['class_grade'].get().strip() or None,
                self.entries['national_id'].get().strip() or None,
                self.entries['phone'].get().strip() or None,
                self.entries['guardian_name'].get().strip() or None,
                self.entries['guardian_phone'].get().strip() or None,
                self.entries['address'].get().strip() or None,
                self.entries['email'].get().strip() or None,
                self.entries['date_of_birth'].get().strip() or None,
                self.gender_var.get(),
                None,  # photo_path
                self.entries['expiry_date'].get().strip(),
                self.membership_type_var.get(),
                self.entries['emergency_contact'].get().strip() or None,
                self.entries['emergency_phone'].get().strip() or None,
                self.entries['notes'].get("0.0", "end").strip() or None
            )

            # Add member to database
            self.db.add_member(member_data)

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('member_added_successfully', 'Member added successfully')
            )

            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_error', 'Error saving member')}: {str(e)}"
            )

class EditMemberDialog(AddMemberDialog):
    def __init__(self, parent, config_manager, db_manager, member):
        self.member = member
        super().__init__(parent, config_manager, db_manager)
        self.title(self.t.get('edit_member', 'Edit Member'))
        self.populate_fields()

    def populate_fields(self):
        """Populate fields with existing member data"""
        try:
            # Map database columns to form fields
            field_mapping = {
                'member_id': self.member[1],
                'name': self.member[2],
                'class_grade': self.member[3] or '',
                'national_id': self.member[4] or '',
                'phone': self.member[5] or '',
                'guardian_name': self.member[6] or '',
                'guardian_phone': self.member[7] or '',
                'address': self.member[8] or '',
                'email': self.member[9] or '',
                'date_of_birth': self.member[10] or '',
                'emergency_contact': self.member[17] or '',
                'emergency_phone': self.member[18] or '',
                'expiry_date': self.member[14] or ''
            }

            # Populate entry fields
            for field_key, value in field_mapping.items():
                if field_key in self.entries:
                    self.entries[field_key].delete(0, "end")
                    self.entries[field_key].insert(0, value)

            # Set gender
            if self.member[11]:
                self.gender_var.set(self.member[11])

            # Set membership type
            if self.member[16]:
                self.membership_type_var.set(self.member[16])

            # Populate notes
            if self.member[19]:
                self.entries['notes'].delete("0.0", "end")
                self.entries['notes'].insert("0.0", self.member[19])

        except Exception as e:
            print(f"Error populating fields: {e}")

    def save_member(self):
        """Update the member"""
        try:
            # Validate required fields
            required_fields = ['member_id', 'name']
            for field in required_fields:
                if not self.entries[field].get().strip():
                    messagebox.showerror(
                        self.t.get('error', 'Error'),
                        f"{self.t.get('field_required', 'Field required')}: {field}"
                    )
                    return

            # Check if member ID already exists (excluding current member)
            existing_member = self.db.execute_query(
                "SELECT id FROM members WHERE member_id = ? AND id != ?",
                (self.entries['member_id'].get().strip(), self.member[0]),
                fetch=True
            )

            if existing_member:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    self.t.get('member_id_already_exists', 'Member ID already exists')
                )
                return

            # Update member in database
            update_query = '''
                UPDATE members SET
                    member_id = ?, name = ?, class_grade = ?, national_id = ?, phone = ?,
                    guardian_name = ?, guardian_phone = ?, address = ?, email = ?, date_of_birth = ?,
                    gender = ?, expiry_date = ?, membership_type = ?, emergency_contact = ?,
                    emergency_phone = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''

            member_data = (
                self.entries['member_id'].get().strip(),
                self.entries['name'].get().strip(),
                self.entries['class_grade'].get().strip() or None,
                self.entries['national_id'].get().strip() or None,
                self.entries['phone'].get().strip() or None,
                self.entries['guardian_name'].get().strip() or None,
                self.entries['guardian_phone'].get().strip() or None,
                self.entries['address'].get().strip() or None,
                self.entries['email'].get().strip() or None,
                self.entries['date_of_birth'].get().strip() or None,
                self.gender_var.get(),
                self.entries['expiry_date'].get().strip(),
                self.membership_type_var.get(),
                self.entries['emergency_contact'].get().strip() or None,
                self.entries['emergency_phone'].get().strip() or None,
                self.entries['notes'].get("0.0", "end").strip() or None,
                self.member[0]  # member ID
            )

            self.db.execute_query(update_query, member_data)

            # Log activity
            self.db.log_activity('UPDATE', 'MEMBER', self.entries['member_id'].get().strip(),
                               f'Updated member: {self.entries["name"].get().strip()}')

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('member_updated_successfully', 'Member updated successfully')
            )

            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('update_error', 'Error updating member')}: {str(e)}"
            )




    def generate_active_members_report(self):
        """Generate active members report"""
        try:
            self.current_report_type = 'active_members'

            # Get active members with current loans
            active_query = '''
                SELECT DISTINCT m.name, m.member_id, m.class_grade, m.phone,
                       COUNT(t.id) as current_loans,
                       GROUP_CONCAT(b.title, '; ') as borrowed_books
                FROM members m
                LEFT JOIN transactions t ON m.member_id = t.member_id
                    AND t.transaction_type = 'borrow'
                    AND t.return_date IS NULL
                LEFT JOIN books b ON t.book_barcode = b.barcode
                WHERE m.status = 'Active'
                GROUP BY m.id, m.name, m.member_id, m.class_grade, m.phone
                ORDER BY current_loans DESC, m.name
            '''

            active_members = self.db.execute_query(active_query, fetch=True)

            report = f"""
{self.t.get('active_members_report', 'ACTIVE MEMBERS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('total_active_members', 'Total Active Members')}: {len(active_members)}

"""

            if active_members:
                members_with_loans = len([m for m in active_members if m[4] > 0])
                total_loans = sum(m[4] for m in active_members)

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('members_with_loans', 'Members with Current Loans')}: {members_with_loans}
{self.t.get('members_without_loans', 'Members without Loans')}: {len(active_members) - members_with_loans}
{self.t.get('total_active_loans', 'Total Active Loans')}: {total_loans}
{self.t.get('average_loans_per_member', 'Average Loans per Member')}: {total_loans / len(active_members):.1f}

{self.t.get('detailed_list', 'DETAILED LIST')}:
{'-' * 40}
"""

                for i, member in enumerate(active_members, 1):
                    status_icon = "📚" if member[4] > 0 else "👤"
                    report += f"""
{i}. {status_icon} {member[0]} (ID: {member[1]})
   {self.t.get('class_grade', 'Class')}: {member[2] or 'N/A'}
   {self.t.get('phone', 'Phone')}: {member[3] or 'N/A'}
   {self.t.get('current_loans', 'Current Loans')}: {member[4]}
"""
                    if member[5] and member[4] > 0:
                        report += f"   {self.t.get('borrowed_books', 'Books')}: {member[5]}\n"
                    report += f"   {'-' * 35}\n"

            else:
                report += f"\n{self.t.get('no_active_members', 'No active members found.')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_monthly_report(self):
        """Generate monthly activity report"""
        try:
            self.current_report_type = 'monthly'

            current_month = datetime.now().strftime('%Y-%m')

            # Get monthly statistics
            monthly_borrows_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow'
                  AND strftime('%Y-%m', transaction_date) = ?
            '''
            monthly_borrows = self.db.execute_query(monthly_borrows_query, (current_month,), fetch=True)[0][0]

            monthly_returns_query = '''
                SELECT COUNT(*) FROM transactions
                WHERE transaction_type = 'borrow'
                  AND return_date IS NOT NULL
                  AND strftime('%Y-%m', return_date) = ?
            '''
            monthly_returns = self.db.execute_query(monthly_returns_query, (current_month,), fetch=True)[0][0]

            new_members_query = '''
                SELECT COUNT(*) FROM members
                WHERE strftime('%Y-%m', registration_date) = ?
            '''
            new_members = self.db.execute_query(new_members_query, (current_month,), fetch=True)[0][0]

            new_books_query = '''
                SELECT COUNT(*) FROM books
                WHERE strftime('%Y-%m', date_added) = ?
            '''
            new_books = self.db.execute_query(new_books_query, (current_month,), fetch=True)[0][0]

            # Get most active members this month
            active_members_query = '''
                SELECT m.name, m.member_id, COUNT(t.id) as borrows_count
                FROM transactions t
                JOIN members m ON t.member_id = m.member_id
                WHERE t.transaction_type = 'borrow'
                  AND strftime('%Y-%m', t.transaction_date) = ?
                GROUP BY m.id, m.name, m.member_id
                ORDER BY borrows_count DESC
                LIMIT 5
            '''
            active_members = self.db.execute_query(active_members_query, (current_month,), fetch=True)

            report = f"""
{self.t.get('monthly_activity_report', 'MONTHLY ACTIVITY REPORT')}
{'=' * 50}

{self.t.get('report_period', 'Report Period')}: {datetime.now().strftime('%B %Y')}
{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{self.t.get('monthly_statistics', 'MONTHLY STATISTICS')}:
{'-' * 30}
{self.t.get('books_borrowed', 'Books Borrowed')}: {monthly_borrows:,}
{self.t.get('books_returned', 'Books Returned')}: {monthly_returns:,}
{self.t.get('new_members', 'New Members')}: {new_members:,}
{self.t.get('new_books', 'New Books Added')}: {new_books:,}

{self.t.get('circulation_activity', 'CIRCULATION ACTIVITY')}:
{'-' * 30}
{self.t.get('net_circulation', 'Net Circulation')}: {monthly_borrows - monthly_returns:,}
{self.t.get('return_rate', 'Return Rate')}: {(monthly_returns/monthly_borrows*100) if monthly_borrows > 0 else 0:.1f}%
"""

            if active_members:
                report += f"""
{self.t.get('most_active_members', 'MOST ACTIVE MEMBERS THIS MONTH')}:
{'-' * 40}
"""
                for i, member in enumerate(active_members, 1):
                    report += f"{i}. {member[0]} (ID: {member[1]}) - {member[2]} {self.t.get('borrows', 'borrows')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_popular_books_report(self):
        """Generate popular books report"""
        try:
            self.current_report_type = 'popular_books'

            # Get most borrowed books
            popular_query = '''
                SELECT b.title, b.author, b.category, b.barcode,
                       COUNT(t.id) as borrow_count,
                       MAX(t.transaction_date) as last_borrowed
                FROM transactions t
                JOIN books b ON t.book_barcode = b.barcode
                WHERE t.transaction_type = 'borrow'
                GROUP BY b.id, b.title, b.author, b.category, b.barcode
                ORDER BY borrow_count DESC, last_borrowed DESC
                LIMIT 20
            '''

            popular_books = self.db.execute_query(popular_query, fetch=True)

            report = f"""
{self.t.get('popular_books_report', 'POPULAR BOOKS REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('top_books', 'Top 20 Most Borrowed Books')}

"""

            if popular_books:
                total_borrows = sum(book[4] for book in popular_books)

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('total_books_analyzed', 'Total Books Analyzed')}: {len(popular_books)}
{self.t.get('total_borrows', 'Total Borrows')}: {total_borrows:,}
{self.t.get('average_borrows', 'Average Borrows per Book')}: {total_borrows / len(popular_books):.1f}

{self.t.get('ranking', 'RANKING')}:
{'-' * 40}
"""

                for i, book in enumerate(popular_books, 1):
                    percentage = (book[4] / total_borrows * 100) if total_borrows > 0 else 0
                    report += f"""
{i:2d}. 📚 {book[0]}
    {self.t.get('author', 'Author')}: {book[1]}
    {self.t.get('category', 'Category')}: {book[2] or 'N/A'}
    {self.t.get('barcode', 'Barcode')}: {book[3]}
    {self.t.get('times_borrowed', 'Times Borrowed')}: {book[4]} ({percentage:.1f}%)
    {self.t.get('last_borrowed', 'Last Borrowed')}: {book[5]}
    {'-' * 35}
"""
            else:
                report += f"\n{self.t.get('no_borrowing_history', 'No borrowing history found.')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def generate_fines_report(self):
        """Generate fines report"""
        try:
            self.current_report_type = 'fines'

            # Get outstanding fines
            fines_query = '''
                SELECT m.name, m.member_id, m.phone, b.title, b.barcode,
                       t.transaction_date, t.due_date, t.fine_amount,
                       julianday('now') - julianday(t.due_date) as days_overdue,
                       t.fine_paid
                FROM transactions t
                JOIN members m ON t.member_id = m.member_id
                JOIN books b ON t.book_barcode = b.barcode
                WHERE t.transaction_type = 'borrow'
                  AND (t.fine_amount > 0 OR t.due_date < date('now'))
                  AND (t.return_date IS NULL OR t.fine_paid = 0)
                ORDER BY t.fine_amount DESC, days_overdue DESC
            '''

            fines_data = self.db.execute_query(fines_query, fetch=True)

            report = f"""
{self.t.get('fines_report', 'FINES REPORT')}
{'=' * 50}

{self.t.get('generated_on', 'Generated on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{self.t.get('fine_per_day', 'Fine per Day')}: {self.config.get('library_settings.fine_per_day', 250)} {self.config.get('library_settings.currency', 'IQD')}

"""

            if fines_data:
                currency = self.config.get('library_settings.currency', 'IQD')
                fine_per_day = self.config.get('library_settings.fine_per_day', 250)

                total_fines = 0
                paid_fines = 0
                unpaid_fines = 0

                for fine in fines_data:
                    if fine[7] > 0:  # existing fine amount
                        if fine[9]:  # fine_paid
                            paid_fines += fine[7]
                        else:
                            unpaid_fines += fine[7]
                        total_fines += fine[7]
                    else:  # calculate fine for overdue
                        days_overdue = max(0, int(fine[8]))
                        calculated_fine = days_overdue * fine_per_day
                        unpaid_fines += calculated_fine
                        total_fines += calculated_fine

                report += f"""
{self.t.get('summary', 'SUMMARY')}:
{self.t.get('total_fines', 'Total Fines')}: {total_fines:,} {currency}
{self.t.get('paid_fines', 'Paid Fines')}: {paid_fines:,} {currency}
{self.t.get('unpaid_fines', 'Unpaid Fines')}: {unpaid_fines:,} {currency}
{self.t.get('collection_rate', 'Collection Rate')}: {(paid_fines/total_fines*100) if total_fines > 0 else 0:.1f}%

{self.t.get('detailed_fines', 'DETAILED FINES LIST')}:
{'-' * 40}
"""

                for i, fine in enumerate(fines_data, 1):
                    days_overdue = max(0, int(fine[8]))
                    fine_amount = fine[7] if fine[7] > 0 else days_overdue * fine_per_day
                    status = "✅ PAID" if fine[9] else "❌ UNPAID"

                    report += f"""
{i}. {fine[0]} (ID: {fine[1]})
   {self.t.get('phone', 'Phone')}: {fine[2] or 'N/A'}
   {self.t.get('book', 'Book')}: {fine[3]}
   {self.t.get('barcode', 'Barcode')}: {fine[4]}
   {self.t.get('borrowed_date', 'Borrowed')}: {fine[5]}
   {self.t.get('due_date', 'Due Date')}: {fine[6]}
   {self.t.get('days_overdue', 'Days Overdue')}: {days_overdue}
   {self.t.get('fine_amount', 'Fine Amount')}: {fine_amount:,} {currency}
   {self.t.get('status', 'Status')}: {status}
   {'-' * 35}
"""
            else:
                report += f"\n🎉 {self.t.get('no_outstanding_fines', 'No outstanding fines found!')}\n"

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            error_msg = f"{self.t.get('error_generating_report', 'Error generating report')}: {str(e)}"
            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", error_msg)

    def export_complete_data(self):
        """Export complete system data"""
        try:
            export_path, info = self.backup_manager.export_data_json()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('data_exported_successfully', 'Data exported successfully')}\n{export_path}"
            )

            # Show export info in report display
            report = f"""
{self.t.get('complete_data_export', 'COMPLETE DATA EXPORT')}
{'=' * 50}

{self.t.get('export_completed', 'Export completed successfully!')}
{self.t.get('file_location', 'File Location')}: {export_path}

{self.t.get('export_information', 'EXPORT INFORMATION')}:
{'-' * 30}
{self.t.get('export_date', 'Export Date')}: {info.get('export_date', 'N/A')}
{self.t.get('system_version', 'System Version')}: {info.get('system_version', 'N/A')}
{self.t.get('language', 'Language')}: {info.get('language', 'N/A')}
{self.t.get('school_name', 'School')}: {info.get('school_info', {}).get('name', 'N/A')}

{self.t.get('data_included', 'DATA INCLUDED')}:
• {self.t.get('all_books', 'All books with complete information')}
• {self.t.get('all_members', 'All members with complete profiles')}
• {self.t.get('all_transactions', 'All transaction history')}
• {self.t.get('system_settings', 'System settings and configuration')}

{self.t.get('file_format', 'File Format')}: JSON
{self.t.get('encoding', 'Encoding')}: UTF-8
{self.t.get('file_size', 'File Size')}: {os.path.getsize(export_path) / 1024:.1f} KB
"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('export_error', 'Export error')}: {str(e)}"
            )

    def backup_data(self):
        """Create system backup"""
        try:
            backup_path, info = self.backup_manager.create_full_backup()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                f"{self.t.get('backup_created_successfully', 'Backup created successfully')}\n{backup_path}"
            )

            # Show backup info in report display
            report = f"""
{self.t.get('system_backup_report', 'SYSTEM BACKUP REPORT')}
{'=' * 50}

{self.t.get('backup_completed', 'Backup completed successfully!')}
{self.t.get('backup_location', 'Backup Location')}: {backup_path}

{self.t.get('backup_information', 'BACKUP INFORMATION')}:
{'-' * 30}
{self.t.get('backup_date', 'Backup Date')}: {info.get('backup_date', 'N/A')}
{self.t.get('system_version', 'System Version')}: {info.get('system_version', 'N/A')}
{self.t.get('database_version', 'Database Version')}: {info.get('database_version', 'N/A')}
{self.t.get('language', 'Language')}: {info.get('language', 'N/A')}
{self.t.get('school_name', 'School')}: {info.get('school_name', 'N/A')}

{self.t.get('backup_contents', 'BACKUP CONTENTS')}:
• {self.t.get('database_file', 'Complete database file')}
• {self.t.get('configuration_files', 'Configuration files')}
• {self.t.get('assets_and_photos', 'Assets and photos')}
• {self.t.get('member_barcodes', 'Member barcodes')}
• {self.t.get('backup_metadata', 'Backup metadata')}

{self.t.get('statistics', 'STATISTICS')}:
{self.t.get('total_books', 'Total Books')}: {info.get('total_books', 0):,}
{self.t.get('total_members', 'Total Members')}: {info.get('total_members', 0):,}
{self.t.get('backup_size', 'Backup Size')}: {os.path.getsize(backup_path) / 1024 / 1024:.1f} MB

{self.t.get('restore_instructions', 'RESTORE INSTRUCTIONS')}:
1. {self.t.get('go_to_settings', 'Go to Settings page')}
2. {self.t.get('click_import_settings', 'Click "Import Settings"')}
3. {self.t.get('select_backup_file', 'Select this backup file')}
4. {self.t.get('confirm_restore', 'Confirm restore operation')}
"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)
            self.current_report_data = report

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('backup_error', 'Backup error')}: {str(e)}"
            )

    def save_current_report(self):
        """Save current report to file"""
        if not hasattr(self, 'current_report_data') or not self.current_report_data:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('no_report_to_save', 'No report to save')
            )
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_type = getattr(self, 'current_report_type', 'report')

            file_path = filedialog.asksaveasfilename(
                title=self.t.get('save_report', 'Save Report'),
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ],
                initialname=f"{report_type}_report_{timestamp}.txt"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_report_data)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('report_saved_successfully', 'Report saved successfully')}\n{file_path}"
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_error', 'Error saving report')}: {str(e)}"
            )

    def print_current_report(self):
        """Print current report"""
        if not hasattr(self, 'current_report_data') or not self.current_report_data:
            messagebox.showwarning(
                self.t.get('warning', 'Warning'),
                self.t.get('no_report_to_print', 'No report to print')
            )
            return

        try:
            # Create temporary HTML file for printing
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_file = os.path.join(tempfile.gettempdir(), f"library_report_{timestamp}.html")

            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Library Report</title>
    <style>
        body {{ font-family: 'Courier New', monospace; margin: 20px; }}
        pre {{ white-space: pre-wrap; word-wrap: break-word; }}
        @media print {{
            body {{ margin: 0; }}
        }}
    </style>
</head>
<body>
    <pre>{self.current_report_data}</pre>
</body>
</html>
"""

            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # Open in browser for printing
            webbrowser.open(f'file://{os.path.abspath(temp_file)}')

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('report_opened_for_printing', 'Report opened in browser for printing')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('print_error', 'Error preparing report for printing')}: {str(e)}"
            )

    def refresh_current_report(self):
        """Refresh current report"""
        if hasattr(self, 'current_report_type') and self.current_report_type:
            report_methods = {
                'statistics': self.generate_statistics_report,
                'overdue': self.generate_overdue_report,
                'active_members': self.generate_active_members_report,
                'monthly': self.generate_monthly_report,
                'popular_books': self.generate_popular_books_report,
                'fines': self.generate_fines_report
            }

            if self.current_report_type in report_methods:
                report_methods[self.current_report_type]()
            else:
                messagebox.showinfo(
                    self.t.get('info', 'Info'),
                    self.t.get('select_report_first', 'Please select a report type first')
                )
        else:
            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('select_report_first', 'Please select a report type first')
            )

    # Settings functionality
    def update_system_info(self):
        """Update system information display"""
        try:
            # Clear existing info
            for widget in self.system_info_display.winfo_children():
                widget.destroy()

            # Get system information
            books_count = len(self.db.get_all_books())
            members_count = len(self.db.get_all_members())
            db_size = os.path.getsize(self.db.db_file) / 1024  # KB

            # System info items
            info_items = [
                (f"📊 {self.t.get('total_books', 'Total Books')}", f"{books_count:,}"),
                (f"👥 {self.t.get('total_members', 'Total Members')}", f"{members_count:,}"),
                (f"💾 {self.t.get('database_size', 'Database Size')}", f"{db_size:.1f} KB"),
                (f"🗂️ {self.t.get('database_file', 'Database File')}", self.db.db_file),
                (f"🌐 {self.t.get('current_language', 'Current Language')}", LANGUAGES[self.current_lang]['name']),
                (f"🎨 {self.t.get('appearance_mode', 'Appearance Mode')}", self.config.get('appearance_mode', 'light')),
                (f"📅 {self.t.get('system_date', 'System Date')}", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            ]

            for label, value in info_items:
                item_frame = ctk.CTkFrame(self.system_info_display)
                item_frame.pack(fill="x", padx=10, pady=2)

                ctk.CTkLabel(item_frame, text=label, font=ctk.CTkFont(size=11, weight="bold")).pack(anchor="w", padx=10, pady=(5, 2))
                ctk.CTkLabel(item_frame, text=value, font=ctk.CTkFont(size=10), wraplength=250).pack(anchor="w", padx=10, pady=(0, 5))

        except Exception as e:
            error_label = ctk.CTkLabel(
                self.system_info_display,
                text=f"{self.t.get('error_loading_system_info', 'Error loading system info')}: {str(e)}",
                font=ctk.CTkFont(size=10)
            )
            error_label.pack(padx=10, pady=10)

    def change_appearance_mode(self, mode):
        """Change appearance mode"""
        try:
            ctk.set_appearance_mode(mode)
            self.config.set('appearance_mode', mode)

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                self.t.get('appearance_changed', 'Appearance mode changed successfully')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('appearance_error', 'Error changing appearance')}: {str(e)}"
            )

    def change_color_theme(self, theme):
        """Change color theme"""
        try:
            ctk.set_default_color_theme(theme)
            self.config.set('color_theme', theme)

            messagebox.showinfo(
                self.t.get('info', 'Info'),
                f"{self.t.get('theme_changed', 'Color theme changed')}. {self.t.get('restart_required', 'Please restart the application to see changes.')}"
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('theme_error', 'Error changing theme')}: {str(e)}"
            )

    def save_settings(self):
        """Save all settings"""
        try:
            # Save school information
            for field_key, entry in self.school_entries.items():
                self.config.set(f'school_info.{field_key}', entry.get().strip())

            # Save library settings
            for field_key, entry in self.library_entries.items():
                value = entry.get().strip()
                if field_key in ['loan_period_days', 'max_books_per_student', 'fine_per_day']:
                    try:
                        value = int(value) if value.isdigit() else self.config.get(f'library_settings.{field_key}')
                    except:
                        value = self.config.get(f'library_settings.{field_key}')

                self.config.set(f'library_settings.{field_key}', value)

            # Save configuration
            self.config.save_config()

            # Update system info
            self.update_system_info()

            messagebox.showinfo(
                self.t.get('success', 'Success'),
                self.t.get('settings_saved_successfully', 'Settings saved successfully')
            )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('save_settings_error', 'Error saving settings')}: {str(e)}"
            )

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno(
            self.t.get('confirm', 'Confirm'),
            self.t.get('reset_settings_confirm', 'Are you sure you want to reset all settings to defaults?')
        ):
            try:
                # Reset to default configuration
                self.config.config = DEFAULT_CONFIG.copy()
                self.config.save_config()

                # Update UI with default values
                for field_key, entry in self.school_entries.items():
                    entry.delete(0, "end")
                    entry.insert(0, DEFAULT_CONFIG['school_info'].get(field_key, ''))

                for field_key, entry in self.library_entries.items():
                    entry.delete(0, "end")
                    entry.insert(0, str(DEFAULT_CONFIG['library_settings'].get(field_key, '')))

                # Reset appearance settings
                self.appearance_mode_var.set(DEFAULT_CONFIG['appearance_mode'])
                self.color_theme_var.set(DEFAULT_CONFIG['color_theme'])

                # Update system info
                self.update_system_info()

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    self.t.get('settings_reset_successfully', 'Settings reset to defaults successfully')
                )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('reset_error', 'Error resetting settings')}: {str(e)}"
                )

    def import_settings(self):
        """Import settings from backup file"""
        file_path = filedialog.askopenfilename(
            title=self.t.get('select_backup_file', 'Select Backup File'),
            filetypes=[
                ("Backup files", "*.zip"),
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.zip'):
                    # Restore from backup
                    success, info = self.backup_manager.restore_from_backup(file_path)
                    if success:
                        messagebox.showinfo(
                            self.t.get('success', 'Success'),
                            f"{self.t.get('backup_restored_successfully', 'Backup restored successfully')}\n"
                            f"{self.t.get('restart_required', 'Please restart the application.')}"
                        )
                elif file_path.endswith('.json'):
                    # Import JSON configuration
                    with open(file_path, 'r', encoding='utf-8') as f:
                        imported_config = json.load(f)

                    # Merge with current config
                    self.config.config = self.config.merge_config(self.config.config, imported_config)
                    self.config.save_config()

                    messagebox.showinfo(
                        self.t.get('success', 'Success'),
                        f"{self.t.get('settings_imported_successfully', 'Settings imported successfully')}\n"
                        f"{self.t.get('restart_required', 'Please restart the application.')}"
                    )

            except Exception as e:
                messagebox.showerror(
                    self.t.get('error', 'Error'),
                    f"{self.t.get('import_error', 'Error importing settings')}: {str(e)}"
                )

    def export_settings(self):
        """Export current settings"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            file_path = filedialog.asksaveasfilename(
                title=self.t.get('export_settings', 'Export Settings'),
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ],
                initialname=f"library_settings_{timestamp}.json"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config.config, f, ensure_ascii=False, indent=4)

                messagebox.showinfo(
                    self.t.get('success', 'Success'),
                    f"{self.t.get('settings_exported_successfully', 'Settings exported successfully')}\n{file_path}"
                )

        except Exception as e:
            messagebox.showerror(
                self.t.get('error', 'Error'),
                f"{self.t.get('export_error', 'Error exporting settings')}: {str(e)}"
            )
