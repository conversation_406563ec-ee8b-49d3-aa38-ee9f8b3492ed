# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Modern UI with CustomTkinter
#                   (Version 6.0 - Modern Design)
# ==============================================================================

import customtkinter as ctk
from tkinter import messagebox, filedialog
from PIL import Image, ImageTk, ImageDraw, ImageFont
import sqlite3
import pandas as pd
import csv
import os
import shutil
from datetime import datetime, timedelta
import barcode
from barcode.writer import ImageWriter
import qrcode
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
import json
import webbrowser
import tempfile
from tkcalendar import DateEntry
import threading

# Import the database and utility classes from the original file
from library_management_system import (
    DatabaseManager, DataManager, IDCardGenerator, 
    ensure_folders, generate_barcode_image, generate_qr_code,
    SCHOOL_INFO, LOGO_FILES, LOAN_PERIOD_DAYS, MAX_BOOKS_PER_STUDENT,
    DATABASE_FILE, REPORTS_FOLDER, BACKUP_FOLDER, BARCODES_FOLDER,
    ASSETS_FOLDER, COVERS_FOLDER, EXPORTS_FOLDER, IMPORTS_FOLDER
)

# ==============================================================================
# Modern UI Configuration
# ==============================================================================

# Set appearance mode and color theme
ctk.set_appearance_mode("light")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

# Modern color scheme
COLORS = {
    'primary': '#1f538d',
    'secondary': '#14375e',
    'accent': '#ffa500',
    'success': '#28a745',
    'warning': '#ffc107',
    'danger': '#dc3545',
    'light': '#f8f9fa',
    'dark': '#343a40',
    'white': '#ffffff',
    'gray': '#6c757d'
}

# ==============================================================================
# Advanced Export Dialog
# ==============================================================================

class AdvancedExportDialog(ctk.CTkToplevel):
    def __init__(self, parent, export_type="books"):
        super().__init__(parent)
        
        self.export_type = export_type
        self.parent = parent
        self.result = None
        
        self.title(f"هەناردەکردنی پێشکەوتوو - {export_type}")
        self.geometry("600x500")
        self.transient(parent)
        self.grab_set()
        
        # Center the dialog
        self.center_window()
        
        self.create_widgets()
    
    def center_window(self):
        """Center the dialog on parent window"""
        self.update_idletasks()
        x = (self.parent.winfo_x() + (self.parent.winfo_width() // 2)) - (self.winfo_width() // 2)
        y = (self.parent.winfo_y() + (self.parent.winfo_height() // 2)) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text=f"هەناردەکردنی {self.export_type}",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Export format selection
        format_frame = ctk.CTkFrame(main_frame)
        format_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(format_frame, text="جۆری فایل:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.format_var = ctk.StringVar(value="excel")
        format_radio_frame = ctk.CTkFrame(format_frame)
        format_radio_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkRadioButton(format_radio_frame, text="Excel (.xlsx)", variable=self.format_var, value="excel").pack(side="left", padx=(0, 20))
        ctk.CTkRadioButton(format_radio_frame, text="CSV (.csv)", variable=self.format_var, value="csv").pack(side="left")
        
        # Date range selection
        date_frame = ctk.CTkFrame(main_frame)
        date_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(date_frame, text="مەودای بەروار:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.use_date_range = ctk.BooleanVar()
        date_check = ctk.CTkCheckBox(date_frame, text="بەکارهێنانی مەودای بەروار", variable=self.use_date_range, command=self.toggle_date_range)
        date_check.pack(anchor="w", padx=15, pady=(0, 10))
        
        # Date range inputs
        self.date_range_frame = ctk.CTkFrame(date_frame)
        self.date_range_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        # From date
        from_frame = ctk.CTkFrame(self.date_range_frame)
        from_frame.pack(fill="x", pady=(10, 5))
        
        ctk.CTkLabel(from_frame, text="لە بەرواری:").pack(side="left", padx=(10, 5))
        self.from_date = DateEntry(from_frame, width=12, background='darkblue',
                                  foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.from_date.pack(side="left", padx=(5, 10))
        
        # To date
        to_frame = ctk.CTkFrame(self.date_range_frame)
        to_frame.pack(fill="x", pady=(5, 10))
        
        ctk.CTkLabel(to_frame, text="تا بەرواری:").pack(side="left", padx=(10, 5))
        self.to_date = DateEntry(to_frame, width=12, background='darkblue',
                                foreground='white', borderwidth=2, date_pattern='yyyy-mm-dd')
        self.to_date.pack(side="left", padx=(5, 10))
        
        # Initially disable date range
        self.toggle_date_range()
        
        # Filter options
        filter_frame = ctk.CTkFrame(main_frame)
        filter_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(filter_frame, text="فلتەرەکان:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        # Status filter
        if self.export_type == "books":
            self.status_var = ctk.StringVar(value="all")
            status_frame = ctk.CTkFrame(filter_frame)
            status_frame.pack(fill="x", padx=15, pady=(0, 10))
            
            ctk.CTkLabel(status_frame, text="دۆخ:").pack(side="left", padx=(10, 5))
            status_menu = ctk.CTkOptionMenu(status_frame, variable=self.status_var,
                                          values=["all", "Available", "Borrowed"])
            status_menu.pack(side="left", padx=(5, 10))
        
        # Include additional data
        options_frame = ctk.CTkFrame(filter_frame)
        options_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.include_stats = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(options_frame, text="لەگەڵ ئاماری گشتی", variable=self.include_stats).pack(anchor="w", padx=10, pady=5)
        
        self.include_summary = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(options_frame, text="لەگەڵ پوختەی ڕاپۆرت", variable=self.include_summary).pack(anchor="w", padx=10, pady=5)
        
        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", pady=(20, 0))
        
        ctk.CTkButton(
            button_frame,
            text="هەناردەکردن",
            command=self.export_data,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="right", padx=(10, 15), pady=15)
        
        ctk.CTkButton(
            button_frame,
            text="پاشگەزبوونەوە",
            command=self.destroy,
            fg_color=COLORS['gray'],
            hover_color=COLORS['dark']
        ).pack(side="right", padx=(15, 0), pady=15)
    
    def toggle_date_range(self):
        """Toggle date range inputs"""
        if self.use_date_range.get():
            for widget in self.date_range_frame.winfo_children():
                for child in widget.winfo_children():
                    if hasattr(child, 'configure'):
                        child.configure(state="normal")
        else:
            for widget in self.date_range_frame.winfo_children():
                for child in widget.winfo_children():
                    if hasattr(child, 'configure') and child != self.date_range_frame:
                        try:
                            child.configure(state="disabled")
                        except:
                            pass
    
    def export_data(self):
        """Export data with selected options"""
        try:
            # Get file path
            if self.format_var.get() == "excel":
                file_path = filedialog.asksaveasfilename(
                    title="پاشەکەوتکردنی فایل",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
            else:
                file_path = filedialog.asksaveasfilename(
                    title="پاشەکەوتکردنی فایل",
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
            
            if not file_path:
                return
            
            # Prepare export options
            options = {
                'format': self.format_var.get(),
                'use_date_range': self.use_date_range.get(),
                'from_date': self.from_date.get_date() if self.use_date_range.get() else None,
                'to_date': self.to_date.get_date() if self.use_date_range.get() else None,
                'include_stats': self.include_stats.get(),
                'include_summary': self.include_summary.get(),
                'file_path': file_path
            }
            
            if hasattr(self, 'status_var'):
                options['status_filter'] = self.status_var.get()
            
            self.result = options
            self.destroy()
            
        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

# ==============================================================================
# Modern Main Application
# ==============================================================================

class ModernLibraryApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Configure window
        self.title("زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ")
        self.geometry("1400x900")
        
        # Initialize managers
        ensure_folders()
        self.db = DatabaseManager()
        self.data_manager = DataManager(self.db)
        self.id_generator = IDCardGenerator(self.db)
        
        # Create UI
        self.create_widgets()
        
        # Load initial data
        self.refresh_data()
    
    def create_widgets(self):
        """Create main UI widgets"""
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.create_main_content()
    
    def create_sidebar(self):
        """Create modern sidebar"""
        self.sidebar_frame = ctk.CTkFrame(self, width=280, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)
        
        # Logo and title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="زانکۆشکا ژین",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="سیستەمی پەرتووکخانە",
            font=ctk.CTkFont(size=14)
        )
        self.subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 20))
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("dashboard", "سەرەکی", "🏠"),
            ("circulation", "خواستن/گەڕاندنەوە", "🔄"),
            ("books", "پەرتووک", "📚"),
            ("members", "ئەندامان", "👥"),
            ("reports", "ڕاپۆرت", "📊"),
            ("settings", "ڕێکخستن", "⚙️")
        ]
        
        for i, (key, text, icon) in enumerate(nav_items):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=f"{icon} {text}",
                command=lambda k=key: self.show_page(k),
                height=40,
                font=ctk.CTkFont(size=14),
                anchor="w"
            )
            btn.grid(row=i+2, column=0, padx=20, pady=5, sticky="ew")
            self.nav_buttons[key] = btn
        
        # Quick stats
        self.stats_frame = ctk.CTkFrame(self.sidebar_frame)
        self.stats_frame.grid(row=8, column=0, padx=20, pady=20, sticky="ew")
        
        self.stats_title = ctk.CTkLabel(
            self.stats_frame,
            text="ئاماری خێرا",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.stats_title.pack(pady=(10, 5))
        
        self.stats_text = ctk.CTkTextbox(self.stats_frame, height=120)
        self.stats_text.pack(padx=10, pady=(0, 10), fill="x")
    
    def create_main_content(self):
        """Create main content area"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # Create pages
        self.pages = {}
        self.create_all_pages()
        
        # Show dashboard by default
        self.show_page("dashboard")
    
    def create_all_pages(self):
        """Create all application pages"""
        # Dashboard page
        self.create_dashboard_page()
        
        # Circulation page
        self.create_circulation_page()
        
        # Books page
        self.create_books_page()
        
        # Members page
        self.create_members_page()
        
        # Reports page
        self.create_reports_page()
        
        # Settings page
        self.create_settings_page()

    def create_dashboard_page(self):
        """Create dashboard page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["dashboard"] = page

        # Page title
        title_frame = ctk.CTkFrame(page)
        title_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            title_frame,
            text="داشبۆرد",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Stats cards
        stats_frame = ctk.CTkFrame(page)
        stats_frame.pack(fill="x", padx=20, pady=10)

        # Create stat cards
        self.stat_cards = {}
        stat_items = [
            ("total_books", "کۆی پەرتووک", "📚", COLORS['primary']),
            ("available_books", "پەرتووکی بەردەست", "✅", COLORS['success']),
            ("borrowed_books", "پەرتووکی خواستراو", "📖", COLORS['warning']),
            ("overdue_books", "پەرتووکی پاشکەوت", "⚠️", COLORS['danger'])
        ]

        for i, (key, title, icon, color) in enumerate(stat_items):
            card = ctk.CTkFrame(stats_frame)
            card.pack(side="left", fill="both", expand=True, padx=10, pady=15)

            ctk.CTkLabel(card, text=icon, font=ctk.CTkFont(size=30)).pack(pady=(15, 5))

            value_label = ctk.CTkLabel(card, text="0", font=ctk.CTkFont(size=24, weight="bold"))
            value_label.pack()
            self.stat_cards[key] = value_label

            ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=12)).pack(pady=(5, 15))

        # Recent activity
        activity_frame = ctk.CTkFrame(page)
        activity_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))

        ctk.CTkLabel(
            activity_frame,
            text="چالاکی نوێ",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(anchor="w", padx=20, pady=(20, 10))

        self.activity_text = ctk.CTkTextbox(activity_frame, height=200)
        self.activity_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_circulation_page(self):
        """Create circulation page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["circulation"] = page

        # Page title
        title_frame = ctk.CTkFrame(page)
        title_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            title_frame,
            text="خواستن و گەڕاندنەوە",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Main content
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left side - Input form
        form_frame = ctk.CTkFrame(content_frame)
        form_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            form_frame,
            text="زانیاری ئەندام و پەرتووک",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Student ID
        ctk.CTkLabel(form_frame, text="ژمارەی ئەندام:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.student_id_entry = ctk.CTkEntry(form_frame, placeholder_text="ژمارەی ئەندام داخڵ بکە")
        self.student_id_entry.pack(fill="x", padx=20, pady=(0, 10))
        self.student_id_entry.bind("<KeyRelease>", self.on_student_id_change)

        # Book barcode
        ctk.CTkLabel(form_frame, text="بارکۆدی پەرتووک:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.book_barcode_entry = ctk.CTkEntry(form_frame, placeholder_text="بارکۆدی پەرتووک داخڵ بکە")
        self.book_barcode_entry.pack(fill="x", padx=20, pady=(0, 15))
        self.book_barcode_entry.bind("<KeyRelease>", self.on_book_barcode_change)

        # Action buttons
        button_frame = ctk.CTkFrame(form_frame)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))

        ctk.CTkButton(
            button_frame,
            text="📖 خواستنی پەرتووک",
            command=self.borrow_book,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary'],
            height=40
        ).pack(fill="x", pady=(10, 5))

        ctk.CTkButton(
            button_frame,
            text="↩️ گەڕاندنەوەی پەرتووک",
            command=self.return_book,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary'],
            height=40
        ).pack(fill="x", pady=5)

        # Right side - Information display
        info_frame = ctk.CTkFrame(content_frame)
        info_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            info_frame,
            text="زانیاری",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Student info
        self.student_info_frame = ctk.CTkFrame(info_frame)
        self.student_info_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(self.student_info_frame, text="ئەندام:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.student_info_label = ctk.CTkLabel(self.student_info_frame, text="هیچ کەسێک دیارنەکراوە")
        self.student_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Book info
        self.book_info_frame = ctk.CTkFrame(info_frame)
        self.book_info_frame.pack(fill="x", padx=20, pady=(0, 15))

        ctk.CTkLabel(self.book_info_frame, text="پەرتووک:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.book_info_label = ctk.CTkLabel(self.book_info_frame, text="هیچ پەرتووکێک دیارنەکراوە")
        self.book_info_label.pack(anchor="w", padx=15, pady=(0, 15))

        # Current loans
        loans_frame = ctk.CTkFrame(info_frame)
        loans_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        ctk.CTkLabel(loans_frame, text="پەرتووکی خواستراو:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        self.loans_text = ctk.CTkTextbox(loans_frame, height=150)
        self.loans_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

    def create_books_page(self):
        """Create books management page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["books"] = page

        # Page title and controls
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="بەڕێوەبردنی پەرتووک",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        button_frame = ctk.CTkFrame(header_frame)
        button_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            button_frame,
            text="➕ پەرتووکی نوێ",
            command=self.add_book_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📥 هاوردەکردن",
            command=self.import_books_csv,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📤 هەناردەکردن",
            command=self.advanced_export_books,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left")

        # Search and filter
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(search_frame, text="گەڕان:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(20, 10), pady=15)

        self.books_search_entry = ctk.CTkEntry(search_frame, placeholder_text="گەڕان بە ناونیشان، نووسەر، یان بارکۆد...")
        self.books_search_entry.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=15)
        self.books_search_entry.bind("<KeyRelease>", self.search_books)

        # Books list
        list_frame = ctk.CTkFrame(page)
        list_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Create scrollable frame for books
        self.books_scroll_frame = ctk.CTkScrollableFrame(list_frame)
        self.books_scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Books will be populated here
        self.books_widgets = []

    def create_members_page(self):
        """Create members management page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["members"] = page

        # Page title and controls
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="بەڕێوەبردنی ئەندامان",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Action buttons
        button_frame = ctk.CTkFrame(header_frame)
        button_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkButton(
            button_frame,
            text="➕ ئەندامی نوێ",
            command=self.add_member_dialog,
            fg_color=COLORS['success'],
            hover_color=COLORS['primary']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="🆔 کارتی ئەندامەتی",
            command=self.generate_member_cards,
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning']
        ).pack(side="left", padx=(0, 10))

        ctk.CTkButton(
            button_frame,
            text="📤 هەناردەکردن",
            command=self.advanced_export_members,
            fg_color=COLORS['primary'],
            hover_color=COLORS['secondary']
        ).pack(side="left")

        # Search
        search_frame = ctk.CTkFrame(page)
        search_frame.pack(fill="x", padx=20, pady=(0, 10))

        ctk.CTkLabel(search_frame, text="گەڕان:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=(20, 10), pady=15)

        self.members_search_entry = ctk.CTkEntry(search_frame, placeholder_text="گەڕان بە ناو، ژمارەی ئەندام، یان پۆل...")
        self.members_search_entry.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=15)
        self.members_search_entry.bind("<KeyRelease>", self.search_members)

        # Members list
        list_frame = ctk.CTkFrame(page)
        list_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        self.members_scroll_frame = ctk.CTkScrollableFrame(list_frame)
        self.members_scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.members_widgets = []

    def create_reports_page(self):
        """Create reports page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["reports"] = page

        # Page title
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="ڕاپۆرت و ئامار",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Content frame
        content_frame = ctk.CTkFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))
        content_frame.grid_columnconfigure((0, 1), weight=1)

        # Left side - Report buttons
        buttons_frame = ctk.CTkFrame(content_frame)
        buttons_frame.grid(row=0, column=0, sticky="nsew", padx=(20, 10), pady=20)

        ctk.CTkLabel(
            buttons_frame,
            text="جۆرەکانی ڕاپۆرت",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        report_buttons = [
            ("📊 ئاماری گشتی", self.generate_statistics_report, COLORS['primary']),
            ("⚠️ پەرتووکی پاشکەوت", self.generate_overdue_report, COLORS['danger']),
            ("👥 ئەندامی چالاک", self.generate_active_members_report, COLORS['success']),
            ("📈 ڕاپۆرتی مانگانە", self.generate_monthly_report, COLORS['accent']),
            ("💾 یەدەگ گرتن", self.backup_data, COLORS['gray']),
            ("📤 هەناردەکردنی تەواو", self.export_complete_data, COLORS['warning'])
        ]

        for text, command, color in report_buttons:
            ctk.CTkButton(
                buttons_frame,
                text=text,
                command=command,
                fg_color=color,
                hover_color=COLORS['secondary'],
                height=40
            ).pack(fill="x", padx=20, pady=5)

        # Right side - Report display
        display_frame = ctk.CTkFrame(content_frame)
        display_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 20), pady=20)

        ctk.CTkLabel(
            display_frame,
            text="ڕاپۆرت",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        self.report_display = ctk.CTkTextbox(display_frame)
        self.report_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_settings_page(self):
        """Create settings page"""
        page = ctk.CTkFrame(self.main_frame)
        self.pages["settings"] = page

        # Page title
        header_frame = ctk.CTkFrame(page)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))

        ctk.CTkLabel(
            header_frame,
            text="ڕێکخستن",
            font=ctk.CTkFont(size=24, weight="bold")
        ).pack(side="left", padx=20, pady=15)

        # Settings content
        content_frame = ctk.CTkScrollableFrame(page)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(10, 20))

        # School information
        school_frame = ctk.CTkFrame(content_frame)
        school_frame.pack(fill="x", pady=(0, 20))

        ctk.CTkLabel(
            school_frame,
            text="زانیاری قوتابخانە",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        school_info = [
            ("ناوی قوتابخانە:", SCHOOL_INFO['name']),
            ("کتێبخانەدار:", SCHOOL_INFO['librarian']),
            ("بەڕێوەبەر:", SCHOOL_INFO['principal']),
            ("ناونیشان:", SCHOOL_INFO['address']),
            ("تەلەفۆن:", SCHOOL_INFO['phone'])
        ]

        for label, value in school_info:
            info_frame = ctk.CTkFrame(school_frame)
            info_frame.pack(fill="x", padx=20, pady=5)

            ctk.CTkLabel(info_frame, text=label, font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)
            ctk.CTkLabel(info_frame, text=value).pack(side="left", padx=(0, 15), pady=10)

        # System settings
        system_frame = ctk.CTkFrame(content_frame)
        system_frame.pack(fill="x", pady=(0, 20))

        ctk.CTkLabel(
            system_frame,
            text="ڕێکخستنەکانی سیستەم",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        # Appearance mode
        appearance_frame = ctk.CTkFrame(system_frame)
        appearance_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(appearance_frame, text="شێوازی دیمەن:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=10)

        self.appearance_mode = ctk.StringVar(value="Light")
        appearance_menu = ctk.CTkOptionMenu(
            appearance_frame,
            variable=self.appearance_mode,
            values=["Light", "Dark", "System"],
            command=self.change_appearance_mode
        )
        appearance_menu.pack(side="right", padx=15, pady=10)

        # Database info
        db_frame = ctk.CTkFrame(content_frame)
        db_frame.pack(fill="x")

        ctk.CTkLabel(
            db_frame,
            text="زانیاری بنکەی داتا",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(20, 15))

        self.db_info_frame = ctk.CTkFrame(db_frame)
        self.db_info_frame.pack(fill="x", padx=20, pady=(0, 20))

    def show_page(self, page_name):
        """Show selected page"""
        # Hide all pages
        for page in self.pages.values():
            page.pack_forget()

        # Show selected page
        if page_name in self.pages:
            self.pages[page_name].pack(fill="both", expand=True)

        # Update navigation buttons
        for key, btn in self.nav_buttons.items():
            if key == page_name:
                btn.configure(fg_color=COLORS['accent'])
            else:
                btn.configure(fg_color=("gray75", "gray25"))

        # Refresh data if needed
        if page_name in ["dashboard", "books", "members"]:
            self.refresh_data()

    def change_appearance_mode(self, new_appearance_mode):
        """Change appearance mode"""
        ctk.set_appearance_mode(new_appearance_mode)

    # Event Handlers
    def on_student_id_change(self, event=None):
        """Handle student ID entry change"""
        student_id = self.student_id_entry.get().strip()
        if student_id:
            member = self.db.find_member_by_id(student_id)
            if member:
                self.student_info_label.configure(text=f"{member[2]} - {member[3] or 'نەدیارکراو'}")
                # Show current loans
                loans = self.db.get_member_borrowed_books(student_id)
                if loans:
                    loans_text = "پەرتووکی خواستراو:\n"
                    for loan in loans:
                        loans_text += f"• {loan[2]} (مەودا: {loan[19]})\n"
                    self.loans_text.delete("0.0", "end")
                    self.loans_text.insert("0.0", loans_text)
                else:
                    self.loans_text.delete("0.0", "end")
                    self.loans_text.insert("0.0", "هیچ پەرتووکێکی خواستراو نییە")
            else:
                self.student_info_label.configure(text="نەدۆزرایەوە!")
                self.loans_text.delete("0.0", "end")
        else:
            self.student_info_label.configure(text="هیچ کەسێک دیارنەکراوە")
            self.loans_text.delete("0.0", "end")

    def on_book_barcode_change(self, event=None):
        """Handle book barcode entry change"""
        barcode = self.book_barcode_entry.get().strip()
        if barcode:
            book = self.db.find_book_by_barcode(barcode)
            if book:
                self.book_info_label.configure(text=f"{book[2]} - {book[14]}")
            else:
                self.book_info_label.configure(text="نەدۆزرایەوە!")
        else:
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")

    def search_books(self, event=None):
        """Search books"""
        search_term = self.books_search_entry.get().strip()
        self.refresh_books_display(search_term)

    def search_members(self, event=None):
        """Search members"""
        search_term = self.members_search_entry.get().strip()
        self.refresh_members_display(search_term)

    # Action Methods
    def borrow_book(self):
        """Handle book borrowing"""
        student_id = self.student_id_entry.get().strip()
        book_barcode = self.book_barcode_entry.get().strip()

        if not student_id or not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە ژمارەی ئەندام و بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            due_date = self.db.borrow_book(book_barcode, student_id)
            messagebox.showinfo("سەرکەوتوو",
                              f"پەرتووک بە سەرکەوتوویی خواسترا\nبەرواری گەڕاندنەوە: {due_date.strftime('%Y-%m-%d')}")

            # Clear entries
            self.student_id_entry.delete(0, "end")
            self.book_barcode_entry.delete(0, "end")
            self.student_info_label.configure(text="هیچ کەسێک دیارنەکراوە")
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")
            self.loans_text.delete("0.0", "end")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە خواستنی پەرتووک: {str(e)}")

    def return_book(self):
        """Handle book returning"""
        book_barcode = self.book_barcode_entry.get().strip()

        if not book_barcode:
            messagebox.showwarning("ئاگاداری", "تکایە بارکۆدی پەرتووک داخڵ بکە")
            return

        try:
            fine_amount = self.db.return_book(book_barcode)

            if fine_amount > 0:
                messagebox.showinfo("سەرکەوتوو",
                                  f"پەرتووک بە سەرکەوتوویی گەڕێندرایەوە\nجەریمە: {fine_amount} دینار")
            else:
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی گەڕێندرایەوە")

            # Clear entries
            self.book_barcode_entry.delete(0, "end")
            self.book_info_label.configure(text="هیچ پەرتووکێک دیارنەکراوە")

            # Refresh data
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە گەڕاندنەوەی پەرتووک: {str(e)}")

    def refresh_data(self):
        """Refresh all data displays"""
        try:
            # Update statistics
            stats = self.db.get_statistics()

            # Update stat cards
            if hasattr(self, 'stat_cards'):
                self.stat_cards['total_books'].configure(text=str(stats['total_books']))
                self.stat_cards['available_books'].configure(text=str(stats['available_books']))
                self.stat_cards['borrowed_books'].configure(text=str(stats['borrowed_books']))
                self.stat_cards['overdue_books'].configure(text=str(stats['overdue_books']))

            # Update sidebar stats
            stats_text = f"""کۆی پەرتووک: {stats['total_books']}
بەردەست: {stats['available_books']}
خواستراو: {stats['borrowed_books']}
پاشکەوت: {stats['overdue_books']}
کۆی ئەندام: {stats['total_members']}
چالاک: {stats['active_members']}"""

            self.stats_text.delete("0.0", "end")
            self.stats_text.insert("0.0", stats_text)

            # Update activity
            if hasattr(self, 'activity_text'):
                activity = self.get_recent_activity()
                self.activity_text.delete("0.0", "end")
                self.activity_text.insert("0.0", activity)

            # Refresh books and members displays
            self.refresh_books_display()
            self.refresh_members_display()

            # Update database info in settings
            if hasattr(self, 'db_info_frame'):
                self.update_db_info()

        except Exception as e:
            print(f"Error refreshing data: {e}")

    def get_recent_activity(self):
        """Get recent activity text"""
        try:
            # Get recent transactions
            query = '''
                SELECT t.transaction_type, b.title, m.name, t.transaction_date
                FROM transactions t
                LEFT JOIN books b ON t.book_barcode = b.barcode
                LEFT JOIN members m ON t.member_id = m.member_id
                ORDER BY t.created_at DESC
                LIMIT 10
            '''
            transactions = self.db.execute_query(query, fetch=True)

            if not transactions:
                return "هیچ چالاکیەک نییە"

            activity_text = "چالاکی نوێ:\n\n"
            for trans in transactions:
                trans_type = "خواستن" if trans[0] == "borrow" else "گەڕاندنەوە"
                activity_text += f"• {trans_type}: {trans[1] or 'نەناسراو'} - {trans[2] or 'نەناسراو'} ({trans[3]})\n"

            return activity_text

        except Exception as e:
            return f"خەڵەتی لە وەرگرتنی چالاکی: {str(e)}"

    def refresh_books_display(self, search_term=""):
        """Refresh books display"""
        try:
            # Clear existing widgets
            for widget in self.books_widgets:
                widget.destroy()
            self.books_widgets.clear()

            # Get books data
            if search_term:
                books = self.db.search_books(search_term)
            else:
                books = self.db.get_all_books()

            # Create book cards
            for book in books[:20]:  # Limit to 20 for performance
                book_card = self.create_book_card(self.books_scroll_frame, book)
                book_card.pack(fill="x", padx=10, pady=5)
                self.books_widgets.append(book_card)

        except Exception as e:
            print(f"Error refreshing books display: {e}")

    def refresh_members_display(self, search_term=""):
        """Refresh members display"""
        try:
            # Clear existing widgets
            for widget in self.members_widgets:
                widget.destroy()
            self.members_widgets.clear()

            # Get members data
            if search_term:
                members = self.db.search_members(search_term)
            else:
                members = self.db.get_all_members()

            # Create member cards
            for member in members[:20]:  # Limit to 20 for performance
                member_card = self.create_member_card(self.members_scroll_frame, member)
                member_card.pack(fill="x", padx=10, pady=5)
                self.members_widgets.append(member_card)

        except Exception as e:
            print(f"Error refreshing members display: {e}")

    def create_book_card(self, parent, book):
        """Create a book card widget"""
        card = ctk.CTkFrame(parent)

        # Book info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Title and author
        title_label = ctk.CTkLabel(info_frame, text=book[2], font=ctk.CTkFont(size=16, weight="bold"))
        title_label.pack(anchor="w")

        author_label = ctk.CTkLabel(info_frame, text=f"نووسەر: {book[3]}")
        author_label.pack(anchor="w", pady=(2, 5))

        # Additional info
        details = f"بارکۆد: {book[1]} | جۆر: {book[8] or 'نەدیارکراو'} | دۆخ: {book[14]}"
        details_label = ctk.CTkLabel(info_frame, text=details, font=ctk.CTkFont(size=12))
        details_label.pack(anchor="w")

        # Status indicator
        status_color = COLORS['success'] if book[14] == 'Available' else COLORS['warning']
        status_frame = ctk.CTkFrame(card, fg_color=status_color)
        status_frame.pack(side="right", fill="y", padx=(0, 15), pady=15)

        status_label = ctk.CTkLabel(status_frame, text=book[14], font=ctk.CTkFont(weight="bold"))
        status_label.pack(padx=15, pady=10)

        return card

    def create_member_card(self, parent, member):
        """Create a member card widget"""
        card = ctk.CTkFrame(parent)

        # Member info
        info_frame = ctk.CTkFrame(card)
        info_frame.pack(side="left", fill="both", expand=True, padx=15, pady=15)

        # Name and ID
        name_label = ctk.CTkLabel(info_frame, text=member[2], font=ctk.CTkFont(size=16, weight="bold"))
        name_label.pack(anchor="w")

        id_label = ctk.CTkLabel(info_frame, text=f"ژمارە: {member[1]}")
        id_label.pack(anchor="w", pady=(2, 5))

        # Additional info
        details = f"پۆل: {member[3] or 'نەدیارکراو'} | تەلەفۆن: {member[5] or 'نەدیارکراو'}"
        details_label = ctk.CTkLabel(info_frame, text=details, font=ctk.CTkFont(size=12))
        details_label.pack(anchor="w")

        # Status indicator
        status_color = COLORS['success'] if member[11] == 'Active' else COLORS['gray']
        status_frame = ctk.CTkFrame(card, fg_color=status_color)
        status_frame.pack(side="right", fill="y", padx=(0, 15), pady=15)

        status_label = ctk.CTkLabel(status_frame, text=member[11], font=ctk.CTkFont(weight="bold"))
        status_label.pack(padx=15, pady=10)

        return card

    def update_db_info(self):
        """Update database info in settings"""
        try:
            # Clear existing info
            for widget in self.db_info_frame.winfo_children():
                widget.destroy()

            stats = self.db.get_statistics()
            db_info = [
                ("کۆی گشتی پەرتووک:", str(stats['total_books'])),
                ("کۆی گشتی ئەندام:", str(stats['total_members'])),
                ("پەرتووکی خواستراو:", str(stats['borrowed_books'])),
                ("پەرتووکی پاشکەوت:", str(stats['overdue_books'])),
                ("فایلی بنکەی داتا:", DATABASE_FILE),
                ("قەبارەی فایل:", self.get_file_size(DATABASE_FILE))
            ]

            for label, value in db_info:
                info_frame = ctk.CTkFrame(self.db_info_frame)
                info_frame.pack(fill="x", pady=2)

                ctk.CTkLabel(info_frame, text=label, font=ctk.CTkFont(weight="bold")).pack(side="left", padx=15, pady=5)
                ctk.CTkLabel(info_frame, text=value).pack(side="left", padx=(0, 15), pady=5)

        except Exception as e:
            print(f"Error updating database info: {e}")

    def get_file_size(self, file_path):
        """Get file size in human readable format"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                for unit in ['B', 'KB', 'MB', 'GB']:
                    if size < 1024.0:
                        return f"{size:.1f} {unit}"
                    size /= 1024.0
                return f"{size:.1f} TB"
            return "نەدۆزرایەوە"
        except:
            return "نەزانراو"

    # Advanced Export Methods
    def advanced_export_books(self):
        """Show advanced export dialog for books"""
        dialog = AdvancedExportDialog(self, "books")
        self.wait_window(dialog)

        if dialog.result:
            self.perform_advanced_export("books", dialog.result)

    def advanced_export_members(self):
        """Show advanced export dialog for members"""
        dialog = AdvancedExportDialog(self, "members")
        self.wait_window(dialog)

        if dialog.result:
            self.perform_advanced_export("members", dialog.result)

    def perform_advanced_export(self, export_type, options):
        """Perform advanced export with options"""
        try:
            # Show progress
            progress_dialog = ctk.CTkToplevel(self)
            progress_dialog.title("هەناردەکردن...")
            progress_dialog.geometry("400x150")
            progress_dialog.transient(self)
            progress_dialog.grab_set()

            progress_label = ctk.CTkLabel(progress_dialog, text="هەناردەکردن لە جێبەجێکردندایە...")
            progress_label.pack(pady=20)

            progress_bar = ctk.CTkProgressBar(progress_dialog)
            progress_bar.pack(pady=10, padx=20, fill="x")
            progress_bar.set(0)

            # Update progress
            self.update()
            progress_bar.set(0.3)

            # Get data based on filters
            if export_type == "books":
                data = self.get_filtered_books_data(options)
            else:
                data = self.get_filtered_members_data(options)

            progress_bar.set(0.6)

            # Export data
            if options['format'] == 'excel':
                self.export_to_excel_advanced(data, options, export_type)
            else:
                self.export_to_csv_advanced(data, options, export_type)

            progress_bar.set(1.0)
            progress_dialog.destroy()

            messagebox.showinfo("سەرکەوتوو", f"داتا بە سەرکەوتوویی هەناردەکرا بۆ:\n{options['file_path']}")

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.destroy()
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def get_filtered_books_data(self, options):
        """Get filtered books data"""
        query = "SELECT * FROM books WHERE 1=1"
        params = []

        # Date range filter
        if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
            query += " AND date_added BETWEEN ? AND ?"
            params.extend([options['from_date'].strftime('%Y-%m-%d'), options['to_date'].strftime('%Y-%m-%d')])

        # Status filter
        if options.get('status_filter') and options['status_filter'] != 'all':
            query += " AND status = ?"
            params.append(options['status_filter'])

        query += " ORDER BY title"

        return self.db.execute_query(query, params, fetch=True)

    def get_filtered_members_data(self, options):
        """Get filtered members data"""
        query = "SELECT * FROM members WHERE 1=1"
        params = []

        # Date range filter
        if options.get('use_date_range') and options.get('from_date') and options.get('to_date'):
            query += " AND registration_date BETWEEN ? AND ?"
            params.extend([options['from_date'].strftime('%Y-%m-%d'), options['to_date'].strftime('%Y-%m-%d')])

        query += " ORDER BY name"

        return self.db.execute_query(query, params, fetch=True)

    def export_to_excel_advanced(self, data, options, export_type):
        """Export to Excel with advanced options"""
        with pd.ExcelWriter(options['file_path'], engine='openpyxl') as writer:
            # Main data sheet
            if export_type == "books":
                columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                          'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                          'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added',
                          'Created At', 'Updated At']
                sheet_name = 'پەرتووک'
            else:
                columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                          'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                          'Registration Date', 'Status', 'Photo Path', 'Created At', 'Updated At']
                sheet_name = 'ئەندامان'

            df = pd.DataFrame(data, columns=columns)
            df.to_excel(writer, sheet_name=sheet_name, index=False)

            # Add statistics sheet if requested
            if options.get('include_stats'):
                stats = self.db.get_statistics()
                stats_data = [
                    ['کۆی گشتی پەرتووک', stats['total_books']],
                    ['پەرتووکی بەردەست', stats['available_books']],
                    ['پەرتووکی خواستراو', stats['borrowed_books']],
                    ['کۆی گشتی ئەندام', stats['total_members']],
                    ['ئەندامی چالاک', stats['active_members']],
                    ['پەرتووکی پاشکەوت', stats['overdue_books']]
                ]
                stats_df = pd.DataFrame(stats_data, columns=['بابەت', 'ژمارە'])
                stats_df.to_excel(writer, sheet_name='ئامار', index=False)

            # Add summary sheet if requested
            if options.get('include_summary'):
                summary_data = [
                    ['بەرواری هەناردەکردن', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                    ['جۆری هەناردەکردن', export_type],
                    ['ژمارەی تۆمار', len(data)],
                    ['فلتەری بەکارهاتوو', 'بەڵێ' if options.get('use_date_range') else 'نەخێر']
                ]

                if options.get('use_date_range'):
                    summary_data.extend([
                        ['لە بەرواری', options['from_date'].strftime('%Y-%m-%d')],
                        ['تا بەرواری', options['to_date'].strftime('%Y-%m-%d')]
                    ])

                summary_df = pd.DataFrame(summary_data, columns=['بابەت', 'نرخ'])
                summary_df.to_excel(writer, sheet_name='پوختە', index=False)

    def export_to_csv_advanced(self, data, options, export_type):
        """Export to CSV with advanced options"""
        if export_type == "books":
            columns = ['ID', 'Barcode', 'Title', 'Author', 'DDC Number', 'Author Code',
                      'Call Number', 'ISBN', 'Category', 'Publisher', 'Publication Year',
                      'Pages', 'Language', 'Summary', 'Status', 'Location', 'Date Added',
                      'Created At', 'Updated At']
        else:
            columns = ['ID', 'Member ID', 'Name', 'Class/Grade', 'National ID', 'Phone',
                      'Guardian Name', 'Guardian Phone', 'Address', 'Email',
                      'Registration Date', 'Status', 'Photo Path', 'Created At', 'Updated At']

        df = pd.DataFrame(data, columns=columns)
        df.to_csv(options['file_path'], index=False, encoding='utf-8-sig')

    # Dialog Methods
    def add_book_dialog(self):
        """Show add book dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("زیادکردنی پەرتووکی نوێ")
        dialog.geometry("600x700")
        dialog.transient(self)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (self.winfo_x() + (self.winfo_width() // 2)) - (dialog.winfo_width() // 2)
        y = (self.winfo_y() + (self.winfo_height() // 2)) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create scrollable form
        scroll_frame = ctk.CTkScrollableFrame(dialog)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        ctk.CTkLabel(scroll_frame, text="زیادکردنی پەرتووکی نوێ",
                    font=ctk.CTkFont(size=20, weight="bold")).pack(pady=(0, 20))

        # Form fields
        fields = {}

        form_data = [
            ("barcode", "بارکۆد *", True),
            ("title", "ناونیشان *", True),
            ("author", "نووسەر *", True),
            ("ddc_number", "ژمارەی DDC", False),
            ("author_code", "کۆدی نووسەر", False),
            ("isbn", "ISBN", False),
            ("category", "جۆر", False),
            ("publisher", "دەرچوون", False),
            ("publication_year", "ساڵی دەرچوون", False),
            ("pages", "ژمارەی لاپەڕە", False),
            ("language", "زمان", False),
            ("location", "شوێن", False)
        ]

        for field_name, label_text, required in form_data:
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", pady=5)

            ctk.CTkLabel(field_frame, text=label_text, font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

            entry = ctk.CTkEntry(field_frame, placeholder_text=f"{label_text} داخڵ بکە")
            entry.pack(fill="x", padx=15, pady=(0, 15))

            if field_name == "language":
                entry.insert(0, "کوردی")

            fields[field_name] = entry

        # Summary field (text area)
        summary_frame = ctk.CTkFrame(scroll_frame)
        summary_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(summary_frame, text="پوختە", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        summary_text = ctk.CTkTextbox(summary_frame, height=80)
        summary_text.pack(fill="x", padx=15, pady=(0, 15))
        fields["summary"] = summary_text

        # Buttons
        button_frame = ctk.CTkFrame(scroll_frame)
        button_frame.pack(fill="x", pady=20)

        def save_book():
            try:
                # Validate required fields
                required_fields = ["barcode", "title", "author"]
                for field in required_fields:
                    if not fields[field].get().strip():
                        messagebox.showwarning("ئاگاداری", f"تکایە {field} پڕ بکەرەوە")
                        return

                # Prepare data
                book_data = [
                    fields["barcode"].get().strip(),
                    fields["title"].get().strip(),
                    fields["author"].get().strip(),
                    fields["ddc_number"].get().strip(),
                    fields["author_code"].get().strip(),
                    f"{fields['ddc_number'].get().strip()} {fields['author_code'].get().strip()}".strip(),
                    fields["isbn"].get().strip(),
                    fields["category"].get().strip(),
                    fields["publisher"].get().strip(),
                    int(fields["publication_year"].get().strip()) if fields["publication_year"].get().strip().isdigit() else None,
                    int(fields["pages"].get().strip()) if fields["pages"].get().strip().isdigit() else None,
                    fields["language"].get().strip() or "کوردی",
                    fields["summary"].get("0.0", "end").strip(),
                    fields["location"].get().strip()
                ]

                self.db.add_book(book_data)
                messagebox.showinfo("سەرکەوتوو", "پەرتووک بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی پەرتووک: {str(e)}")

        ctk.CTkButton(button_frame, text="پاشەکەوتکردن", command=save_book,
                     fg_color=COLORS['success']).pack(side="left", padx=(15, 10), pady=15)
        ctk.CTkButton(button_frame, text="پاشگەزبوونەوە", command=dialog.destroy,
                     fg_color=COLORS['gray']).pack(side="left", padx=(0, 15), pady=15)

    def add_member_dialog(self):
        """Show add member dialog"""
        dialog = ctk.CTkToplevel(self)
        dialog.title("زیادکردنی ئەندامی نوێ")
        dialog.geometry("600x600")
        dialog.transient(self)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (self.winfo_x() + (self.winfo_width() // 2)) - (dialog.winfo_width() // 2)
        y = (self.winfo_y() + (self.winfo_height() // 2)) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create scrollable form
        scroll_frame = ctk.CTkScrollableFrame(dialog)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        ctk.CTkLabel(scroll_frame, text="زیادکردنی ئەندامی نوێ",
                    font=ctk.CTkFont(size=20, weight="bold")).pack(pady=(0, 20))

        # Form fields
        fields = {}

        form_data = [
            ("member_id", "ژمارەی ئەندام *", True),
            ("name", "ناو *", True),
            ("class_grade", "پۆل", False),
            ("national_id", "ژمارەی ناسنامە", False),
            ("phone", "تەلەفۆن", False),
            ("guardian_name", "ناوی سەرپەرشتیار", False),
            ("guardian_phone", "تەلەفۆنی سەرپەرشتیار", False),
            ("address", "ناونیشان", False),
            ("email", "ئیمەیڵ", False)
        ]

        for field_name, label_text, required in form_data:
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", pady=5)

            ctk.CTkLabel(field_frame, text=label_text, font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

            entry = ctk.CTkEntry(field_frame, placeholder_text=f"{label_text} داخڵ بکە")
            entry.pack(fill="x", padx=15, pady=(0, 15))

            fields[field_name] = entry

        # Buttons
        button_frame = ctk.CTkFrame(scroll_frame)
        button_frame.pack(fill="x", pady=20)

        def save_member():
            try:
                # Validate required fields
                if not fields["member_id"].get().strip() or not fields["name"].get().strip():
                    messagebox.showwarning("ئاگاداری", "تکایە خانەکانی پێویست پڕ بکەرەوە")
                    return

                # Prepare data
                member_data = [
                    fields["member_id"].get().strip(),
                    fields["name"].get().strip(),
                    fields["class_grade"].get().strip(),
                    fields["national_id"].get().strip(),
                    fields["phone"].get().strip(),
                    fields["guardian_name"].get().strip(),
                    fields["guardian_phone"].get().strip(),
                    fields["address"].get().strip(),
                    fields["email"].get().strip()
                ]

                self.db.add_member(member_data)
                messagebox.showinfo("سەرکەوتوو", "ئەندام بە سەرکەوتوویی زیادکرا")
                dialog.destroy()
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە زیادکردنی ئەندام: {str(e)}")

        ctk.CTkButton(button_frame, text="پاشەکەوتکردن", command=save_member,
                     fg_color=COLORS['success']).pack(side="left", padx=(15, 10), pady=15)
        ctk.CTkButton(button_frame, text="پاشگەزبوونەوە", command=dialog.destroy,
                     fg_color=COLORS['gray']).pack(side="left", padx=(0, 15), pady=15)

    # Import Methods
    def import_books_csv(self):
        """Import books from CSV"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ پەرتووک",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_books_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} پەرتووک هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    def import_members_csv(self):
        """Import members from CSV"""
        file_path = filedialog.askopenfilename(
            title="هەڵبژاردنی فایلی CSV بۆ ئەندام",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                imported_count, errors = self.data_manager.import_members_from_csv(file_path)

                message = f"بە سەرکەوتوویی {imported_count} ئەندام هاوردەکرا"
                if errors:
                    message += f"\n{len(errors)} خەڵەتی هەبوو"

                messagebox.showinfo("ئەنجامی هاوردەکردن", message)
                self.refresh_data()

            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هاوردەکردن: {str(e)}")

    # Report Methods
    def generate_statistics_report(self):
        """Generate statistics report"""
        try:
            stats = self.db.get_statistics()

            report = f"""ڕاپۆرتی ئاماری گشتی
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

پەرتووک:
  کۆی گشتی: {stats['total_books']}
  بەردەست: {stats['available_books']}
  خواستراو: {stats['borrowed_books']}
  پاشکەوت: {stats['overdue_books']}

ئەندام:
  کۆی گشتی: {stats['total_members']}
  چالاک: {stats['active_members']}

ڕێژەکان:"""

            if stats['total_books'] > 0:
                available_percent = (stats['available_books'] / stats['total_books']) * 100
                borrowed_percent = (stats['borrowed_books'] / stats['total_books']) * 100
                report += f"""
  پەرتووکی بەردەست: {available_percent:.1f}%
  پەرتووکی خواستراو: {borrowed_percent:.1f}%"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_overdue_report(self):
        """Generate overdue books report"""
        try:
            overdue_books = self.db.get_overdue_books()

            report = f"""ڕاپۆرتی پەرتووکی پاشکەوت
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
کۆی گشتی: {len(overdue_books)} پەرتووک
{'='*50}

"""

            if overdue_books:
                for book in overdue_books:
                    days_overdue = (datetime.now().date() - datetime.strptime(book[4], '%Y-%m-%d').date()).days
                    fine = days_overdue * 250
                    report += f"""پەرتووک: {book[0]}
نووسەر: {book[1]}
ئەندام: {book[2]} ({book[3]})
بەرواری گەڕاندنەوە: {book[4]}
ڕۆژی پاشکەوت: {days_overdue}
جەریمە: {fine} دینار
{'-'*30}
"""
            else:
                report += "هیچ پەرتووکێکی پاشکەوت نییە."

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_active_members_report(self):
        """Generate active members report"""
        try:
            query = '''
                SELECT DISTINCT m.member_id, m.name, m.class_grade, COUNT(t.id) as borrowed_count
                FROM members m
                JOIN transactions t ON m.member_id = t.member_id
                WHERE t.transaction_type = 'borrow' AND t.return_date IS NULL
                GROUP BY m.member_id, m.name, m.class_grade
                ORDER BY borrowed_count DESC
            '''
            active_members = self.db.execute_query(query, fetch=True)

            report = f"""ڕاپۆرتی ئەندامی چالاک
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
کۆی گشتی: {len(active_members)} ئەندام
{'='*50}

"""

            if active_members:
                for member in active_members:
                    report += f"""ئەندام: {member[1]}
ژمارە: {member[0]}
پۆل: {member[2] or 'نەدیارکراو'}
پەرتووکی خواستراو: {member[3]}
{'-'*30}
"""
            else:
                report += "هیچ ئەندامێکی چالاک نییە."

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_monthly_report(self):
        """Generate monthly report"""
        try:
            current_month = datetime.now().strftime('%Y-%m')

            # Get monthly statistics
            query = '''
                SELECT
                    COUNT(CASE WHEN transaction_type = 'borrow' THEN 1 END) as borrows,
                    COUNT(CASE WHEN transaction_type = 'return' THEN 1 END) as returns
                FROM transactions
                WHERE strftime('%Y-%m', transaction_date) = ?
            '''
            monthly_stats = self.db.execute_query(query, (current_month,), fetch=True)[0]

            report = f"""ڕاپۆرتی مانگانە - {current_month}
بەرواری دروستکردن: {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*50}

چالاکی مانگ:
  خواستنی پەرتووک: {monthly_stats[0]}
  گەڕاندنەوەی پەرتووک: {monthly_stats[1]}

ئاماری ئێستا:"""

            stats = self.db.get_statistics()
            report += f"""
  کۆی پەرتووک: {stats['total_books']}
  پەرتووکی خواستراو: {stats['borrowed_books']}
  پەرتووکی پاشکەوت: {stats['overdue_books']}
  کۆی ئەندام: {stats['total_members']}
  ئەندامی چالاک: {stats['active_members']}"""

            self.report_display.delete("0.0", "end")
            self.report_display.insert("0.0", report)

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی ڕاپۆرت: {str(e)}")

    def generate_member_cards(self):
        """Generate member ID cards"""
        try:
            result = messagebox.askyesno("دڵنیایی", "ئایا دەتەوێت کارتی ئەندامەتی بۆ هەموو ئەندامان دروست بکەیت؟")
            if result:
                # Show progress
                progress_dialog = ctk.CTkToplevel(self)
                progress_dialog.title("دروستکردنی کارت...")
                progress_dialog.geometry("400x150")
                progress_dialog.transient(self)
                progress_dialog.grab_set()

                progress_label = ctk.CTkLabel(progress_dialog, text="کارتەکان دروست دەکرێن...")
                progress_label.pack(pady=20)

                progress_bar = ctk.CTkProgressBar(progress_dialog)
                progress_bar.pack(pady=10, padx=20, fill="x")
                progress_bar.set(0)

                self.update()

                # Generate cards in background
                def generate_cards():
                    try:
                        card_paths = self.id_generator.generate_all_member_cards()
                        progress_bar.set(0.8)

                        if card_paths:
                            html_file = self.id_generator.create_cards_html_page(card_paths)
                            progress_bar.set(1.0)

                            progress_dialog.destroy()

                            messagebox.showinfo("سەرکەوتوو",
                                              f"بە سەرکەوتوویی {len(card_paths)} کارت دروستکرا\n"
                                              f"فایلی HTML: {html_file}")

                            if messagebox.askyesno("کردنەوە", "ئایا دەتەوێت فایلی HTML بکەیتەوە؟"):
                                webbrowser.open(html_file)
                        else:
                            progress_dialog.destroy()
                            messagebox.showwarning("ئاگاداری", "هیچ کارتێک دروست نەکرا")
                    except Exception as e:
                        progress_dialog.destroy()
                        messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی کارت: {str(e)}")

                # Run in thread to prevent UI freezing
                threading.Thread(target=generate_cards, daemon=True).start()

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی کارت: {str(e)}")

    def export_complete_data(self):
        """Export complete data"""
        file_path = filedialog.asksaveasfilename(
            title="پاشەکەوتکردنی ڕاپۆرتی تەواو",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.data_manager.export_complete_report_to_excel(file_path)
                messagebox.showinfo("سەرکەوتوو", f"ڕاپۆرتی تەواو بە سەرکەوتوویی هەناردەکرا بۆ:\n{file_path}")
            except Exception as e:
                messagebox.showerror("خەڵەتی", f"خەڵەتی لە هەناردەکردن: {str(e)}")

    def backup_data(self):
        """Create data backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = os.path.join(BACKUP_FOLDER, f"library_backup_{timestamp}")

            shutil.make_archive(backup_filename, 'zip', root_dir='.')

            messagebox.showinfo("سەرکەوتوو", f"یەدەگ بە سەرکەوتوویی دروستکرا:\n{backup_filename}.zip")

        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دروستکردنی یەدەگ: {str(e)}")

# ==============================================================================
# Main Execution
# ==============================================================================

if __name__ == "__main__":
    try:
        app = ModernLibraryApp()
        app.mainloop()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردنی بەرنامە: {str(e)}")
