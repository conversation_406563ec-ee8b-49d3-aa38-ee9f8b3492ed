#!/usr/bin/env python3
# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Application Launcher
# ==============================================================================

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

class LibraryLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("زانکۆشکا ژین - هەڵبژاردنی ڕووکار")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        self.create_widgets()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create launcher widgets"""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="زانکۆشکا ژین",
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#1f538d'
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(
            main_frame,
            text="سیستەمێ برێڤەبرنا پەرتووکخانێ",
            font=('Arial', 16),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Description
        desc_label = tk.Label(
            main_frame,
            text="تکایە جۆری ڕووکار هەڵبژێرە:",
            font=('Arial', 14, 'bold'),
            bg='#f0f0f0'
        )
        desc_label.pack(pady=(0, 20))
        
        # Option 1: Modern UI
        modern_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        modern_frame.pack(fill='x', pady=(0, 15))
        
        modern_title = tk.Label(
            modern_frame,
            text="🎨 ڕووکاری نوێ (پێشنیارکراو)",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#1f538d'
        )
        modern_title.pack(pady=(15, 5))
        
        modern_desc = tk.Label(
            modern_frame,
            text="• ڕووکاری نوێ و جوان\n• تایبەتمەندی پێشکەوتوو بۆ هەناردەکردن\n• فلتەری بەروار و هەڵبژاردەکان\n• ئەداکاری باشتر",
            font=('Arial', 11),
            bg='white',
            justify='left'
        )
        modern_desc.pack(pady=(0, 10))
        
        modern_btn = tk.Button(
            modern_frame,
            text="دەستپێکردنی ڕووکاری نوێ",
            command=self.launch_modern,
            bg='#28a745',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10
        )
        modern_btn.pack(pady=(0, 15))
        
        # Option 2: Classic UI
        classic_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        classic_frame.pack(fill='x', pady=(0, 20))
        
        classic_title = tk.Label(
            classic_frame,
            text="📋 ڕووکاری کلاسیکی",
            font=('Arial', 16, 'bold'),
            bg='white',
            fg='#6c757d'
        )
        classic_title.pack(pady=(15, 5))
        
        classic_desc = tk.Label(
            classic_frame,
            text="• ڕووکاری ئاسایی\n• هەموو تایبەتمەندییە بنەڕەتییەکان\n• کەمتر سەرچاوە بەکاردێنێت",
            font=('Arial', 11),
            bg='white',
            justify='left'
        )
        classic_desc.pack(pady=(0, 10))
        
        classic_btn = tk.Button(
            classic_frame,
            text="دەستپێکردنی ڕووکاری کلاسیکی",
            command=self.launch_classic,
            bg='#6c757d',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10
        )
        classic_btn.pack(pady=(0, 15))
        
        # Exit button
        exit_btn = tk.Button(
            main_frame,
            text="دەرچوون",
            command=self.root.quit,
            bg='#dc3545',
            fg='white',
            font=('Arial', 12),
            relief='flat',
            padx=20,
            pady=5
        )
        exit_btn.pack(pady=(10, 0))
    
    def launch_modern(self):
        """Launch modern UI"""
        try:
            if os.path.exists("modern_library_system.py"):
                subprocess.Popen([sys.executable, "modern_library_system.py"])
                self.root.quit()
            else:
                messagebox.showerror("خەڵەتی", "فایلی ڕووکاری نوێ نەدۆزرایەوە!")
        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردن: {str(e)}")
    
    def launch_classic(self):
        """Launch classic UI"""
        try:
            if os.path.exists("library_management_system.py"):
                subprocess.Popen([sys.executable, "library_management_system.py"])
                self.root.quit()
            else:
                messagebox.showerror("خەڵەتی", "فایلی ڕووکاری کلاسیکی نەدۆزرایەوە!")
        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردن: {str(e)}")
    
    def run(self):
        """Run the launcher"""
        self.root.mainloop()

if __name__ == "__main__":
    launcher = LibraryLauncher()
    launcher.run()
