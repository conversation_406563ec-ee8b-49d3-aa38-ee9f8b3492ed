#!/usr/bin/env python3
# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Installation Script
# ==============================================================================

import subprocess
import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

class InstallationWizard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("زانکۆشکا ژین - دامەزراندن")
        self.root.geometry("650x600")
        self.root.resizable(True, True)
        self.root.minsize(600, 500)

        # Set window icon if available
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # Center window
        self.center_window()

        self.create_widgets()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create installation wizard widgets"""
        # Main frame with grid layout
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Configure main frame grid
        main_frame.grid_rowconfigure(5, weight=1)  # Make log frame expandable
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="زانکۆشکا ژین",
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#1f538d'
        )
        title_label.grid(row=0, column=0, pady=(0, 5), sticky='ew')

        subtitle_label = tk.Label(
            main_frame,
            text="دامەزراندنی سیستەمی پەرتووکخانە",
            font=('Arial', 16),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.grid(row=1, column=0, pady=(0, 20), sticky='ew')
        
        # Welcome message
        welcome_text = """بەخێربێیت بۆ سیستەمی بەڕێوەبردنی پەرتووکخانەی زانکۆشکا ژین!

ئەم بەرنامەیە هەموو پێداویستییەکانی پەرتووکخانەیەکی نوێژین دابین دەکات:

✓ بەڕێوەبردنی پەرتووک و ئەندامان
✓ خواستن و گەڕاندنەوەی پەرتووک
✓ دروستکردنی کارتی ئەندامەتی و لیبڵی پەرتووک
✓ ڕاپۆرت و ئاماری تەواو
✓ هاوردە و هەناردەکردنی داتا

بۆ دەستپێکردن، تکایە دوگمەی "دەستپێکردنی دامەزراندن" کلیک بکە."""
        
        welcome_label = tk.Label(
            main_frame,
            text=welcome_text,
            font=('Arial', 11),
            bg='#f0f0f0',
            justify='right',
            wraplength=550
        )
        welcome_label.grid(row=2, column=0, pady=(0, 20), sticky='ew')

        # Progress frame
        self.progress_frame = tk.Frame(main_frame, bg='#f0f0f0')
        self.progress_frame.grid(row=3, column=0, sticky='ew', pady=(0, 20))
        
        self.progress_label = tk.Label(
            self.progress_frame,
            text="ئامادە بۆ دامەزراندن",
            font=('Arial', 12),
            bg='#f0f0f0'
        )
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(pady=(10, 0))
        
        # Log text
        self.log_frame = tk.Frame(main_frame, bg='white', relief='sunken', bd=2)
        self.log_frame.grid(row=4, column=0, sticky='nsew', pady=(0, 20))

        # Add scrollbar to log text
        log_scroll = tk.Scrollbar(self.log_frame)
        log_scroll.pack(side='right', fill='y')

        self.log_text = tk.Text(
            self.log_frame,
            height=8,
            font=('Consolas', 9),
            bg='white',
            fg='black',
            yscrollcommand=log_scroll.set
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        log_scroll.config(command=self.log_text.yview)

        # Buttons frame - fixed at bottom
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.grid(row=5, column=0, sticky='ew', pady=(10, 0))

        # Configure button frame
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(2, weight=1)

        self.install_btn = tk.Button(
            button_frame,
            text="🚀 دەستپێکردنی دامەزراندن",
            command=self.start_installation,
            bg='#28a745',
            fg='white',
            font=('Arial', 14, 'bold'),
            relief='raised',
            bd=3,
            padx=40,
            pady=15,
            cursor='hand2',
            activebackground='#218838',
            activeforeground='white'
        )
        self.install_btn.grid(row=0, column=0, padx=10, pady=10, sticky='w')

        # Add status label in center
        self.status_label = tk.Label(
            button_frame,
            text="ئامادە بۆ دامەزراندن",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#666666'
        )
        self.status_label.grid(row=0, column=1, padx=20, pady=10)

        self.close_btn = tk.Button(
            button_frame,
            text="❌ دەرچوون",
            command=self.root.quit,
            bg='#dc3545',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='raised',
            bd=3,
            padx=30,
            pady=15,
            cursor='hand2',
            activebackground='#c82333',
            activeforeground='white'
        )
        self.close_btn.grid(row=0, column=2, padx=10, pady=10, sticky='e')
    
    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_installation(self):
        """Start the installation process"""
        self.install_btn.config(state='disabled', text="دامەزراندن لە جێبەجێکردندایە...")
        self.status_label.config(text="دامەزراندن دەستپێدەکات...", fg='#007bff')
        self.progress_bar['value'] = 0

        try:
            self.log_message("🚀 دەستپێکردنی دامەزراندن...")
            self.progress_label.config(text="پشکنینی سیستەم...")

            # Check Python version
            self.log_message(f"🐍 Python version: {sys.version.split()[0]}")
            self.progress_bar['value'] = 10
            self.root.update()

            # Install requirements
            self.log_message("📦 دامەزراندنی پێداویستییەکان...")
            self.progress_label.config(text="دامەزراندنی پاکێجەکان...")
            self.status_label.config(text="دامەزراندنی پاکێجەکان...", fg='#ffc107')
            self.install_requirements()
            self.progress_bar['value'] = 50
            self.root.update()

            # Create directories
            self.log_message("📁 دروستکردنی فۆڵدەرەکان...")
            self.progress_label.config(text="دروستکردنی فۆڵدەرەکان...")
            self.status_label.config(text="دروستکردنی فۆڵدەرەکان...", fg='#17a2b8')
            self.create_directories()
            self.progress_bar['value'] = 70
            self.root.update()

            # Initialize database
            self.log_message("🗄️ دروستکردنی بنکەی داتا...")
            self.progress_label.config(text="دروستکردنی بنکەی داتا...")
            self.status_label.config(text="دروستکردنی بنکەی داتا...", fg='#6f42c1')
            self.initialize_database()
            self.progress_bar['value'] = 90
            self.root.update()

            # Final setup
            self.log_message("🎯 تەواوکردنی دامەزراندن...")
            self.progress_label.config(text="دامەزراندن تەواو بوو!")
            self.status_label.config(text="دامەزراندن تەواو بوو!", fg='#28a745')
            self.progress_bar['value'] = 100
            self.root.update()

            self.log_message("✅ دامەزراندن بە سەرکەوتوویی تەواو بوو!")
            self.log_message("🎉 دەتوانیت ئێستا بەرنامەکە بەکاربهێنیت.")

            # Enable launch button
            self.install_btn.config(
                text="🚀 دەستپێکردنی بەرنامە",
                command=self.launch_application,
                bg='#007bff',
                state='normal',
                activebackground='#0056b3'
            )

        except Exception as e:
            self.log_message(f"❌ خەڵەتی لە دامەزراندن: {str(e)}")
            self.progress_label.config(text="دامەزراندن سەرکەوتوو نەبوو!")
            self.status_label.config(text="دامەزراندن سەرکەوتوو نەبوو!", fg='#dc3545')
            self.install_btn.config(
                state='normal',
                text="🔄 دووبارە هەوڵبدەرەوە",
                bg='#ffc107',
                activebackground='#e0a800'
            )
    
    def install_requirements(self):
        """Install required packages"""
        try:
            # Check if requirements.txt exists
            if os.path.exists('requirements.txt'):
                self.log_message("دامەزراندنی پاکێجەکان لە requirements.txt...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_message("✅ هەموو پاکێجەکان بە سەرکەوتوویی دامەزران")
                else:
                    self.log_message(f"⚠️ هەندێک کێشە لە دامەزراندنی پاکێجەکان: {result.stderr}")
            else:
                self.log_message("⚠️ فایلی requirements.txt نەدۆزرایەوە")
                
        except Exception as e:
            raise Exception(f"خەڵەتی لە دامەزراندنی پاکێجەکان: {str(e)}")
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            'reports', 'backup', 'member_barcodes', 'assets', 
            'covers', 'exports', 'imports'
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.log_message(f"✅ فۆڵدەری {directory} دروستکرا")
            else:
                self.log_message(f"ℹ️ فۆڵدەری {directory} پێشتر هەیە")
    
    def initialize_database(self):
        """Initialize the database"""
        try:
            # Import and initialize database
            from library_management_system import DatabaseManager
            db = DatabaseManager()
            self.log_message("✅ بنکەی داتا بە سەرکەوتوویی دروستکرا")
        except Exception as e:
            raise Exception(f"خەڵەتی لە دروستکردنی بنکەی داتا: {str(e)}")
    
    def launch_application(self):
        """Launch the application"""
        try:
            if os.path.exists('launcher.py'):
                subprocess.Popen([sys.executable, 'launcher.py'])
                self.root.quit()
            else:
                messagebox.showerror("خەڵەتی", "فایلی launcher.py نەدۆزرایەوە!")
        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردن: {str(e)}")
    
    def run(self):
        """Run the installation wizard"""
        self.root.mainloop()

if __name__ == "__main__":
    wizard = InstallationWizard()
    wizard.run()
