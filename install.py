#!/usr/bin/env python3
# ==============================================================================
#           زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
#                   Installation Script
# ==============================================================================

import subprocess
import sys
import os
import tkinter as tk
from tkinter import messagebox, ttk

class InstallationWizard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("زانکۆشکا ژین - دامەزراندن")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # Center window
        self.center_window()
        
        self.create_widgets()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create installation wizard widgets"""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="زانکۆشکا ژین",
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#1f538d'
        )
        title_label.pack(pady=(0, 5))
        
        subtitle_label = tk.Label(
            main_frame,
            text="دامەزراندنی سیستەمی پەرتووکخانە",
            font=('Arial', 16),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack(pady=(0, 20))
        
        # Welcome message
        welcome_text = """بەخێربێیت بۆ سیستەمی بەڕێوەبردنی پەرتووکخانەی زانکۆشکا ژین!

ئەم بەرنامەیە هەموو پێداویستییەکانی پەرتووکخانەیەکی نوێژین دابین دەکات:

✓ بەڕێوەبردنی پەرتووک و ئەندامان
✓ خواستن و گەڕاندنەوەی پەرتووک
✓ دروستکردنی کارتی ئەندامەتی و لیبڵی پەرتووک
✓ ڕاپۆرت و ئاماری تەواو
✓ هاوردە و هەناردەکردنی داتا

بۆ دەستپێکردن، تکایە دوگمەی "دەستپێکردنی دامەزراندن" کلیک بکە."""
        
        welcome_label = tk.Label(
            main_frame,
            text=welcome_text,
            font=('Arial', 11),
            bg='#f0f0f0',
            justify='right',
            wraplength=550
        )
        welcome_label.pack(pady=(0, 20))
        
        # Progress frame
        self.progress_frame = tk.Frame(main_frame, bg='#f0f0f0')
        self.progress_frame.pack(fill='x', pady=(0, 20))
        
        self.progress_label = tk.Label(
            self.progress_frame,
            text="ئامادە بۆ دامەزراندن",
            font=('Arial', 12),
            bg='#f0f0f0'
        )
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(pady=(10, 0))
        
        # Log text
        self.log_frame = tk.Frame(main_frame, bg='white', relief='sunken', bd=2)
        self.log_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        self.log_text = tk.Text(
            self.log_frame,
            height=8,
            font=('Consolas', 9),
            bg='white',
            fg='black'
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x')
        
        self.install_btn = tk.Button(
            button_frame,
            text="دەستپێکردنی دامەزراندن",
            command=self.start_installation,
            bg='#28a745',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10
        )
        self.install_btn.pack(side='left')
        
        self.close_btn = tk.Button(
            button_frame,
            text="دەرچوون",
            command=self.root.quit,
            bg='#dc3545',
            fg='white',
            font=('Arial', 12),
            relief='flat',
            padx=20,
            pady=10
        )
        self.close_btn.pack(side='right')
    
    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_installation(self):
        """Start the installation process"""
        self.install_btn.config(state='disabled')
        self.progress_bar['value'] = 0
        
        try:
            self.log_message("دەستپێکردنی دامەزراندن...")
            self.progress_label.config(text="دامەزراندنی پێداویستییەکان...")
            
            # Check Python version
            self.log_message(f"Python version: {sys.version}")
            self.progress_bar['value'] = 10
            
            # Install requirements
            self.log_message("دامەزراندنی پێداویستییەکان...")
            self.install_requirements()
            self.progress_bar['value'] = 50
            
            # Create directories
            self.log_message("دروستکردنی فۆڵدەرەکان...")
            self.create_directories()
            self.progress_bar['value'] = 70
            
            # Initialize database
            self.log_message("دروستکردنی بنکەی داتا...")
            self.initialize_database()
            self.progress_bar['value'] = 90
            
            # Final setup
            self.log_message("تەواوکردنی دامەزراندن...")
            self.progress_bar['value'] = 100
            self.progress_label.config(text="دامەزراندن تەواو بوو!")
            
            self.log_message("✅ دامەزراندن بە سەرکەوتوویی تەواو بوو!")
            self.log_message("دەتوانیت ئێستا بەرنامەکە بەکاربهێنیت.")
            
            # Enable launch button
            self.install_btn.config(
                text="دەستپێکردنی بەرنامە",
                command=self.launch_application,
                bg='#007bff',
                state='normal'
            )
            
        except Exception as e:
            self.log_message(f"❌ خەڵەتی لە دامەزراندن: {str(e)}")
            self.progress_label.config(text="دامەزراندن سەرکەوتوو نەبوو!")
            self.install_btn.config(state='normal')
    
    def install_requirements(self):
        """Install required packages"""
        try:
            # Check if requirements.txt exists
            if os.path.exists('requirements.txt'):
                self.log_message("دامەزراندنی پاکێجەکان لە requirements.txt...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_message("✅ هەموو پاکێجەکان بە سەرکەوتوویی دامەزران")
                else:
                    self.log_message(f"⚠️ هەندێک کێشە لە دامەزراندنی پاکێجەکان: {result.stderr}")
            else:
                self.log_message("⚠️ فایلی requirements.txt نەدۆزرایەوە")
                
        except Exception as e:
            raise Exception(f"خەڵەتی لە دامەزراندنی پاکێجەکان: {str(e)}")
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            'reports', 'backup', 'member_barcodes', 'assets', 
            'covers', 'exports', 'imports'
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.log_message(f"✅ فۆڵدەری {directory} دروستکرا")
            else:
                self.log_message(f"ℹ️ فۆڵدەری {directory} پێشتر هەیە")
    
    def initialize_database(self):
        """Initialize the database"""
        try:
            # Import and initialize database
            from library_management_system import DatabaseManager
            db = DatabaseManager()
            self.log_message("✅ بنکەی داتا بە سەرکەوتوویی دروستکرا")
        except Exception as e:
            raise Exception(f"خەڵەتی لە دروستکردنی بنکەی داتا: {str(e)}")
    
    def launch_application(self):
        """Launch the application"""
        try:
            if os.path.exists('launcher.py'):
                subprocess.Popen([sys.executable, 'launcher.py'])
                self.root.quit()
            else:
                messagebox.showerror("خەڵەتی", "فایلی launcher.py نەدۆزرایەوە!")
        except Exception as e:
            messagebox.showerror("خەڵەتی", f"خەڵەتی لە دەستپێکردن: {str(e)}")
    
    def run(self):
        """Run the installation wizard"""
        self.root.mainloop()

if __name__ == "__main__":
    wizard = InstallationWizard()
    wizard.run()
