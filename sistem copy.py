books_df = pd.read_excel(excel_file, sheet_name="پەرتوک")
        members_df = pd.read_excel(excel_file, sheet_name="خوێنەر")
        borrowed_df = pd.read_excel(excel_file, sheet_name="بەردراو")
    except FileNotFoundError:
        print("فایلێ داتایێ نەهاتە دیتن، فایلەکێ نوی دهێتە دروستکرن.")
        books_df = pd.DataFrame(columns=["ژمارە", "ناوی پەرتوک", "نووسەر", "کۆدی پەرتوک", "جۆر", "ژمارەی گشتی", "ژمارەی بەردراو", "ژمارەی بەردەست"])
        members_df = pd.DataFrame(columns=["ژمارە", "ناوی خوێنەر", "پۆل", "ژمارەی ناسنامە", "ژمارەی مۆبایل"])
        borrowed_df = pd.DataFrame(columns=["ژمارەی ئەندام", "ژمارەی پەرتوک", "رێکەفتا برنێ", "رێکەفتا ئینانێ"])
    return books_df, members_df, borrowed_df

# نڤیسینا هەمی داتایان بۆ فایلێ
def write_data(books_df, members_df, borrowed_df):
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        books_df.to_excel(writer, sheet_name="پەرتوک", index=False)
        members_df.to_excel(writer, sheet_name="خوێنەر", index=False)
        borrowed_df.to_excel(writer, sheet_name="بەردراو", index=False)
    print("داتا ب سەرکەفتیانە هاتە پاشکەفتکرن.")

# زێدەکرنا پەرتووکەکا نوی
def add_book(books_df):
    try:
        book_id = int(input("ژمارە (ID) یا پەرتووکێ داخل بکە: "))
        if book_id in books_df["ژمارە"].values:
            print("❌ خەلەتی: ئەڤ ژمارەیە بەرێ هاتیە بکارئینان.")
            return books_df
        
        name = input("ناڤێ پەرتووکێ: ")
        author = input("ناڤێ نڤیسەری: ")
        code = input("کۆدێ پەرتووکێ: ")
        kind = input("جورێ پەرتووکێ: ")
        total = int(input("هژمارا گشتی یا پەرتووکێ: "))

        new_book = {
            "ژمارە": book_id,
            "ناوی پەرتوک": name,
            "نووسەر": author,
            "کۆدی پەرتوک": code,
            "جۆر": kind,
            "ژمارەی گشتی": total,
            "ژمارەی بەردراو": 0,
            "ژمارەی بەردەست": total
        }
        
        new_book_df = pd.DataFrame([new_book])
        books_df = pd.concat([books_df, new_book_df], ignore_index=True)
        print(f"✅ پەرتووکا '{name}' ب سەرکەفتیانە هاتە زێدەکرن.")
    except ValueError:
        print("❌ خەلەتی: تکایە بۆ 'ژمارە' و 'هژمارا گشتی' بتنێ ژمارەیان داخل بکە.")
    return books_df

# زێدەکرنا ئەندامەکێ نوی
def add_member(members_df):
    try:
        member_id = int(input("ژمارە (ID) یا ئەندامی داخل بکە: "))
        if member_id in members_df["ژمارە"].values:
            print("❌ خەلەتی: ئەڤ ژمارەیە بەرێ هاتیە بکارئینان.")
            return members_df
            
        name = input("ناڤێ ئەندامی: ")
        grade = input("پۆل/ئاست: ")
        national_id = input("ژمارەیا ناسنامێ: ")
        mobile = input("ژمارەیا موبایلێ: ")

        new_member = {
            "ژمارە": member_id,
            "ناوی خوێنەر": name,
            "پۆل": grade,
            "ژمارەی ناسنامە": national_id,
            "ژمارەی مۆبایل": mobile
        }

        new_member_df = pd.DataFrame([new_member])
        members_df = pd.concat([members_df, new_member_df], ignore_index=True)
        print(f"✅ ئەندام '{name}' ب سەرکەفتیانە هاتە زێدەکرن.")
    except ValueError:
        print("❌ خەلەتی: تکایە بۆ 'ژمارە' ژمارەیەکا درست داخل بکە.")
    return members_df

# پرۆسەیا برنا پەرتووکێ
def borrow_book(books_df, members_df, borrowed_df):
    try:
        member_id = int(input("ژمارە (ID) یا وی ئەندامی داخل بکە یێ پەرتووکێ دبەت: "))
        if member_id not in members_df["ژمارە"].values:
            print("❌ خەلەتی: ئەڤ ئەندامە نەهاتە دیتن.")
            return borrowed_df, books_df

        book_id = int(input("ژمارە (ID) یا وێ پەرتووکێ داخل بکە یا کو دهێتە برن: "))
        if book_id not in books_df["ژمارە"].values:
            print("❌ خەلەتی: ئەڤ پەرتووکە نەهاتە دیتن.")
            return borrowed_df, books_df

        book_row = books_df.loc[books_df["ژمارە"] == book_id]
        available = book_row["ژمارەی بەردەست"].iloc[0]

        if available > 0:
            borrow_date = date.today()
            due_date = borrow_date + timedelta(days=14)  # بۆ نموونە ١٤ رۆژ
            
            new_borrow = {
                "ژمارەی ئەندام": member_id, 
                "ژمارەی پەرتوک": book_id,
                "رێکەفتا برنێ": borrow_date.strftime("%Y-%m-%d"),
                "رێکەفتا ئینانێ": due_date.strftime("%Y-%m-%d")
            }

            new_borrow_df = pd.DataFrame([new_borrow])
            borrowed_df = pd.concat([borrowed_df, new_borrow_df], ignore_index=True)
            
            idx = books_df.index[books_df["ژمارە"] == book_id][0]
            books_df.at[idx, "ژمارەی بەردراو"] += 1
            books_df.at[idx, "ژمارەی بەردەست"] -= 1
            
            print("✅ پەرتووک ب سەرکەفتیانە هاتە دان.")
        else:
            print("⚠️ ببورە، چ دانە ژ ڤێ پەرتووکێ نینن کو بهێنە دان.")
    except ValueError:
        print("❌ خەلەتی: تکایە بۆ ژمارەیان بتنێ ژمارەیان داخل بکە.")
    return borrowed_df, books_df

# پرۆسەیا ئینانا پەرتووکێ
def return_book(books_df, borrowed_df):
    try:
        member_id = int(input("ژمارە (ID) یا ئەندامی داخل بکە: "))
        book_id = int(input("ژمارە (ID) یا پەرتووکا کو دهێتە ئینان داخل بکە: "))

        # ل комбинаسیونا ئەندام و پەرتووکێ دگەریێت
        match = borrowed_df[(borrowed_df["ژمارەی ئەندام"] == member_id) & (borrowed_df["ژمارەی پەرتوک"] == book_id)]
        
        if not match.empty:
            # لادبرنا رێکۆردێ ژ لیستا برنان
            borrowed_df = borrowed_df.drop(match.index)
            
            # زێدەکرنا هژمارا پەرتووکان
            idx = books_df.index[books_df["ژمارە"] == book_id][0]
            books_df.at[idx, "ژمارەی بەردراو"] -= 1
            books_df.at[idx, "ژمارەی بەردەست"] += 1
            
            book_name = books_df.loc[idx, "ناوی پەرتوک"]
            print(f"✅ پەرتووکا '{book_name}' هاتە ڤەگەڕاندن.")
        else:
            print("❌ خەلەتی: ئەڤ پەرتووکە ژلایێ ڤی ئەندامی ڤە نەهاتیە برن یان زانیاری خەلەتن.")
    except ValueError:
        print("❌ خەلەتی: تکایە بۆ ژمارەیان بتنێ ژمارەیان داخل بکە.")
    return borrowed_df, books_df

# گەڕیان ل پەرتووکان
def search_books(books_df):
    query = input("لدیڤ ناڤێ پەرتووکێ یان نڤیسەری بگەڕێ: ").strip()
    if not query:
        return
        
    results = books_df[books_df['ناوی پەرتوک'].str.contains(query, case=False, na=False) | 
                       books_df['نووسەر'].str.contains(query, case=False, na=False)]
    
    if results.empty:
        print("ℹ️ چ ئەنجامەک نەهاتە دیتن.")
    else:
        print("\n--- ئەنجامێن گەڕیانێ ---")
        print(results.to_string(index=False))

# گەڕیان ل ئەندامان
def search_members(members_df):
    query = input("لدیڤ ناڤێ ئەندامی یان ژمارەیا ناسنامێ بگەڕێ: ").strip()
    if not query:
        return
        
    results = members_df[members_df['ناوی خوێنەر'].str.contains(query, case=False, na=False) | 
                         members_df['ژمارەی ناسنامە'].astype(str).str.contains(query, na=False)]
    
    if results.empty:
        print("ℹ️ چ ئەنجامەک نەهاتە دیتن.")
    else:
        print("\n--- ئەنجامێن گەڕیانێ ---")
        print(results.to_string(index=False))

# نیشاندانا هەمی لیستان
def show_lists(books_df, members_df, borrowed_df):
    print("\n--- لیستا هەمی پەرتووکان ---")
    if books_df.empty:
        print("چ پەرتووکەک نینە.")
    else:
        print(books_df.to_string(index=False))

    print("\n--- لیستا هەمی ئەندامان ---")
    if members_df.empty:
        print("چ ئەندامەک نینە.")
    else:
        print(members_df.to_string(index=False))

    print("\n--- لیستا پەرتووکێن هاتینە برن ---")
    if borrowed_df.empty:
        print("چ پەرتووکەک نەهاتیە برن.")
    else:
        # لێکگرێدانا زانیاریان بۆ نیشاندانەکا باشتر
        merged_df = pd.merge(borrowed_df, books_df[['ژمارە', 'ناوی پەرتوک']], left_on='ژمارەی پەرتوک', right_on='ژمارە', how='left')
        merged_df = pd.merge(merged_df, members_df[['ژمارە', 'ناوی خوێنەر']], left_on='ژمارەی ئەندام', right_on='ژمارە', how='left')
        
        display_df = merged_df[['ناوی خوێنەر', 'ناوی پەرتوک', 'رێکەفتا برنێ', 'رێکەفتا ئینانێ']]
        print(display_df.to_string(index=False))

# پرێنتکرنا مێنیویێ
def print_menu():
    print("\n╔═══════════════════════════════════╗")
    print("║   سیستەمێ رێڤەبەرنا پەرتووکخانێ   ║")
    print("╠═══════════════════════════════════╣")
    print("║ ١. زێدەکرنا پەرتووکەکێ              ║")
    print("║ ٢. زێدەکرنا ئەندامەکی               ║")
    print("║ ٣. برنا پەرتووکەکێ (دان)           ║")
    print("║ ٤. ئینانا پەرتووکەکێ (ڤەگەڕاندن)   ║")
    print("║ ٥. گەڕیان ل پەرتووکان             ║")
    print("║ ٦. گەڕیان ل ئەندامان              ║")
    print("║ ٧. نیشاندانا هەمی لیستان           ║")
    print("║ ٨. دەرکەفتن و پاشکەفتکرن         ║")
    print("╚═══════════════════════════════════╝")

# فەنکشنێ سەرەکی
def main():
    books_df, members_df, borrowed_df = read_data()
    while True:
        print_menu()
        choice = input("هەلبژاردنەکێ داخل بکە (١-٨): ")
        
        if choice == "1":
            books_df = add_book(books_df)
        elif choice == "2":
            books_df = add_member(members_df)
        elif choice == "3":
            borrowed_df, books_df = borrow_book(books_df, members_df, borrowed_df)
        elif choice == "4":
            borrowed_df, books_df = return_book(books_df, borrowed_df)
        elif choice == "5":
            search_books(books_df)
        elif choice == "6":
            search_members(members_df)
        elif choice == "7":
            show_lists(books_df, members_df, borrowed_df)
        elif choice == "8":
            write_data(books_df, members_df, borrowed_df)
            print("\nسوپاس بۆ بکارئینانا سیستەمی. ب خاترێ تە.")
            break
        else:
            print("❌ خەلەتی: تکایە ژمارەیەکا درست دناڤبەرا ١ و ٨ دا هەلبژێرە.")

if __name__ == "__main__":
    main()