# زانکۆشکا ژین - سیستەمێ برێڤەبرنا پەرتووکخانێ
# Zhin School - Complete Library Management System

## پێناسە / Description

سیستەمێکی تەواو بۆ بەڕێوەبردنی پەرتووکخانە بە زمانی کوردی کە بە Python و Tkinter دروستکراوە. ئەم سیستەمە هەموو تایبەتمەندییەکانی پێویست بۆ بەڕێوەبردنی پەرتووکخانەیەکی نوێژین لەخۆدەگرێت.

A complete library management system in Kurdish language built with Python and Tkinter. This system includes all necessary features for managing a modern library.

## تایبەتمەندییەکان / Features

### بنەڕەتی / Core Features
- ✅ بەڕێوەبردنی پەرتووک (زیادکردن، گەڕان، دەستکاریکردن)
- ✅ بەڕێوەبردنی ئەندامان (تۆمارکردن، زانیاری کەسی)
- ✅ خواستن و گەڕاندنەوەی پەرتووک
- ✅ حیسابکردنی جەریمە بۆ پەرتووکی پاشکەوت
- ✅ گەڕان و فلتەرکردنی پێشکەوتوو

### هاوردە و هەناردەکردن / Import/Export
- ✅ هاوردەکردن لە فایلی CSV (پەرتووک و ئەندامان)
- ✅ هەناردەکردن بۆ Excel (تاکە یان کۆمەڵ)
- ✅ ڕاپۆرتی تەواو بە چەندین شیت

### دروستکردنی کارت و لیبڵ / ID Cards & Labels
- ✅ کارتی ئەندامەتی بە لۆگۆ و بارکۆد
- ✅ لیبڵی پەرتووک بە DDC و بارکۆد
- ✅ دروستکردنی فایلی HTML بۆ چاپکردن
- ✅ پشتگیری لە لۆگۆی فەرمی

### ڕاپۆرت و ئامار / Reports & Analytics
- ✅ ڕاپۆرتی پەرتووکی پاشکەوت
- ✅ ڕاپۆرتی ئەندامی چالاک
- ✅ ئاماری گشتی پەرتووکخانە
- ✅ یەدەگ گرتنی ئۆتۆماتیک

### بنکەی داتا / Database
- ✅ SQLite بۆ پاشەکەوتکردنی داتا
- ✅ پەیوەندی نێوان خشتەکان
- ✅ ئیندێکسکردن بۆ گەڕانی خێرا

## دامەزراندن / Installation

### پێداویستییەکان / Requirements
```bash
pip install -r requirements.txt
```

### کۆپیکردنی فایلەکان / File Setup
1. لۆگۆکان لە فۆڵدەری سەرەکی دابنێ:
   - `لۆگۆیا حکومەتا هەرێما کوردستانێ.png`
   - `لۆگۆیا وەزارەتا پەروەردەیێ.png`
   - `لۆگۆیا قوتابخانا ژین.png`

2. فۆڵدەرەکان بە خۆکار دروست دەبن:
   - `reports/` - بۆ ڕاپۆرتەکان
   - `exports/` - بۆ هەناردەکردن
   - `imports/` - بۆ هاوردەکردن
   - `backup/` - بۆ یەدەگ
   - `member_barcodes/` - بۆ بارکۆدەکان

## بەکارهێنان / Usage

### دەستپێکردن / Starting
```bash
python library_management_system.py
```

### هاوردەکردنی داتا / Importing Data
1. فایلی CSV ئامادە بکە (نموونە: `sample_books.csv`, `sample_members.csv`)
2. لە تابی پەرتووک/ئەندام، دوگمەی "هاوردەکردن لە CSV" کلیک بکە
3. فایلەکە هەڵبژێرە

### دروستکردنی کارت / Generating Cards
1. بچۆ بۆ تابی "ئەندام"
2. "دروستکردنی کارتی ئەندامەتی" کلیک بکە
3. فایلی HTML دروست دەبێت بۆ چاپکردن

### هەناردەکردن / Exporting
- تاکە: لە تابی پەرتووک/ئەندام "هەناردەکردن بۆ Excel"
- تەواو: لە تابی ڕاپۆرت "هەناردەکردنی تەواوی داتا"

## ڕێکخستن / Configuration

### زانیاری قوتابخانە / School Information
فایلی `library_management_system.py` دەستکاری بکە:
```python
SCHOOL_INFO = {
    'name': 'ناوی قوتابخانەکەت',
    'librarian': 'ناوی کتێبخانەدار',
    'principal': 'ناوی بەڕێوەبەر',
    'address': 'ناونیشان',
    'phone': 'ژمارەی تەلەفۆن'
}
```

### ڕێکخستنەکانی خواستن / Loan Settings
```python
LOAN_PERIOD_DAYS = 14  # ماوەی خواستن بە ڕۆژ
MAX_BOOKS_PER_STUDENT = 3  # زۆرترین پەرتووک بۆ هەر ئەندامێک
```

## پشتگیری / Support

بۆ هەر پرسیار یان کێشەیەک، تکایە پەیوەندی بکە.

## مۆڵەت / License

ئەم بەرنامەیە بۆ بەکارهێنانی قوتابخانەکان و دامەزراوە پەروەردەییەکان بە خۆڕایی بەردەستە.

---

**تێبینی گرنگ:** پێش بەکارهێنان، دڵنیابە کە هەموو لۆگۆکان لە شوێنی خۆیاندان و پێداویستییەکان دامەزراون.
